<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'lead_verification' => [
        /**
         * The driver that is used for verifying leads
         *
         * 'dummy' => A fake driver that can be used in dev.
         * 'identity_check' => A driver that utilises the WhitePages IdentityCheck service
         */
        'driver' => env('LEAD_VERIFICATION_DRIVER', 'dummy')
    ],

    'consumer_product_verification' => [
        /**
         * The driver that is used for verifying consumer products
         *
         * 'dummy'          => A fake driver that can be used in dev.
         * 'identity_check' => A driver that utilises the WhitePages IdentityCheck service
         */
        'driver' => env('CONSUMER_PRODUCT_VERIFICATION_DRIVER', 'dummy')
    ],

    'twilio' => [
        'sid' => env('TWILIO_SID', ''),
        'token' => env('TWILIO_TOKEN', ''),
        'ml_sid' => env('TWILIO_ML_SID', ''),
        'from_phone_number' => env('TWILIO_FROM_PHONE_NUMBER', ''),
        'consumer_verification_from_phone' => env('TWILIO_CONSUMER_VERIFICATION_FROM_PHONE', ''),
        'recycled_leads_from_phone' => env('TWILIO_RECYCLED_LEADS_FROM_PHONE', ''),
        'verify_service_sid' => env('TWILIO_VERIFY_SERVICE_SID', ''),
        'review_requests' => env('TWILIO_REVIEW_REQUESTS', '')
    ],

    'whitepages' => [
        'identity_verification' => [
            "api_key" => env('WHITEPAGES_IDENTITY_VERIFICATION_KEY'),
            "api_url" => env("WHITEPAGES_IDENTITY_VERIFICATION_URL", "https://api.ekata.com/3.3/identity_check")
        ]
    ],

    'google' => [
        'application_name'  => env('APP_NAME'),
        'client_id'         => env('GOOGLE_GMAIL_CLIENT_ID'),
        'client_secret'     => env('GOOGLE_GMAIL_CLIENT_SECRET'),

        'gmail' => [
            'application_name'  => env('APP_NAME'),
            'client_id'         => env('GOOGLE_GMAIL_CLIENT_ID'),
            'client_secret'     => env('GOOGLE_GMAIL_CLIENT_SECRET'),
            'email_listener_topic' => env('GOOGLE_GMAIL_EMAIL_LISTENER_TOPIC')
        ],
        'maps' => [
            'static_maps_key' => env('GOOGLE_STATIC_MAPS_API_KEY', null),
            'places_base_uri' => env('GOOGLE_MAPS_PLACES_API_URI'),
            'geocoding_api_key' => env('GOOGLE_MAPS_GEOCODING_API_KEY')
        ],
        'company_discovery' => [
            'places_api_key' => env('COMPANY_DISCOVERY_GOOGLE_PLACES_API_KEY', null)
        ],
        'key_file' => env('GOOGLE_CLOUD_KEY_FILE', null),
        'pubsub' => [
            'service_account' => env('GOOGLE_CLOUD_KEY_FILE', null),
            'project_id' => env('GOOGLE_PUBSUB_PROJECT_ID'),
            'topic' => env('GOOGLE_PUBSUB_TOPIC'),
            'debug' => env('GOOGLE_PUBSUB_DEBUG', true),
        ],
        'firestore' => [
            'project_id' => env("GOOGLE_FIRESTORE_PROJECT_ID"),
            'key_file' => env("GOOGLE_FIRESTORE_KEY_FILE"),
            'collection_prefix' => env("GOOGLE_FIRESTORE_COLLECTION_PREFIX"),
            'flow_proxy_url' => env('FLOW_PROXY_URL'),
            'flow_proxy_secret' => env('FLOW_PROXY_SECRET'),
        ],
        'storage' => [
            'service_account' => env('GOOGLE_CLOUD_KEY_FILE', null),
            'project_id' => env('GOOGLE_STORAGE_PROJECT_ID'),
            'base_url' => env('GOOGLE_STORAGE_BASE_URL', 'https://storage.googleapis.com'),
            'buckets' => [
                'email_template_images' => env('GOOGLE_STORAGE_EMAIL_TEMPLATE_IMAGES_BUCKET'),
                'tinymce_files' => env('GOOGLE_STORAGE_TINYMCE_FILES_BUCKET'),
                'company_logos' => env('GOOGLE_STORAGE_COMPANY_LOGO_BUCKET', env('GOOGLE_STORAGE_TINYMCE_FILES_BUCKET')),
                'contracts' => env('GOOGLE_STORAGE_CONTRACT_BUCKET'),
                'mailbox' => env('GOOGLE_STORAGE_MAILBOX_BUCKET'),
                'invoices' => env('GOOGLE_STORAGE_INVOICES_BUCKET', env('GOOGLE_STORAGE_TINYMCE_FILES_BUCKET')),
                'review_attachments' => env('REVIEW_ATTACHMENTS_BUCKET'),
            ],
            'urls' => [
                'tinymce_file_url' => env('GOOGLE_STORAGE_TINYMCE_FILE_URL'),
                'company_logos_file_url' => env('GOOGLE_STORAGE_COMPANY_LOGO_URL', env('GOOGLE_STORAGE_TINYMCE_FILE_URL')),
                'flow_builder_image_bucket' => env('FLOW_BUILDER_IMAGE_BUCKET'),
                'flow_builder_cdn_url' => env('FLOW_BUILDER_CDN_URL', "https://dev-cdn.admin-sr.com/"),
                'contracts_url' => env('GOOGLE_STORAGE_CONTRACT_URL'),
                'mailbox_cdn_url' => env('GOOGLE_STORAGE_MAILBOX_CDN_URL')
            ],
            'company_logos_disk' => env('COMPANY_LOGOS_DISK', env('TIME_MCE_STORAGE_DISK', 'public'))
        ],
        'logging' => [
            'project_id' => env('GOOGLE_LOGGING_PROJECT_ID'),
            'debug_monitoring_logs' => env('GOOGLE_DEBUG_MONITORING_LOGS')
        ],
        'stackdriver' => [
            'enabled' => env('STACKDRIVER_ENABLED', false),
            'project_id' => env('GOOGLE_STACKDRIVER_PROJECT_ID', env('GOOGLE_PUBSUB_PROJECT_ID', null)),
        ],
        'ads' => [
            'developer_token' => env('GOOGLE_ADS_DEVELOPER_TOKEN'),
            'client_id' => env('GOOGLE_ADS_CLIENT_ID'),
            'client_secret' => env('GOOGLE_ADS_CLIENT_SECRET'),
            'refresh_token' => env('GOOGLE_ADS_REFRESH_TOKEN'),
            'geotargets_csv_url' => env('GOOGLE_ADS_GEOTARGETS_CSV_URL'),
            'impersonated_email' => env('GOOGLE_ADS_IMPERSONATED_EMAIL'),
            'auth_driver' => env('GOOGLE_ADS_AUTH_DRIVER', 'refresh-token'),
            'conversion_action_name' => env('GOOGLE_ADS_CONVERSION_ACTION_NAME'),
            'login_customer_id' => env('GOOGLE_ADS_LOGIN_CUSTOMER_ID'),
            'test_account_id' => env('GOOGLE_ADS_TEST_ACCOUNT_ID')
        ],
        'solar' => [
            'base_url' => env('GOOGLE_SOLAR_API_BASE_URL', 'https://solar.googleapis.com/v1/detectedArrays:findClosest'),
            'api_key' => env('GOOGLE_SOLAR_API_KEY', ''),
        ]
    ],

    'microsoft' => [
        'ads' => [
            'customer_id' => env('MICROSOFT_ADS_CUSTOMER_ID'),
            'offline_conversion_goal_name' => env('MICROSOFT_ADS_OFFLINE_CONVERSION_GOAL_NAME'),
            'developer_token' => env('MICROSOFT_ADS_DEVELOPER_TOKEN'),
            'client_id' => env('MICROSOFT_ADS_CLIENT_ID'),
            'client_secret' => env('MICROSOFT_ADS_CLIENT_SECRET'),
            'oauth_redirect_uri' => env('MICROSOFT_ADS_OAUTH_REDIRECT_URI')
        ]
    ],

    //facebook
    'meta' => [
        'ads' => [
            'admin_system_user_token' => env("META_ADS_ADMIN_SYSTEM_USER_TOKEN"),
            'business_id' => env("META_ADS_BUSINESS_ID"),
            'app_id' => env("META_ADS_APP_ID"),
            'app_secret' => env("META_ADS_APP_SECRET"),
            'automation_system_user_name' => env('META_ADS_AUTOMATION_SYSTEM_USER_NAME')
        ],
        'wade_ads' => [
            'admin_system_user_token' => env("META_WADE_ADS_ADMIN_SYSTEM_USER_TOKEN"),
            'business_id' => env("META_WADE_ADS_BUSINESS_ID"),
            'app_id' => env("META_WADE_ADS_APP_ID"),
            'app_secret' => env("META_WADE_ADS_APP_SECRET"),
            'pixel_id' => env("META_WADE_ADS_PIXEL_ID")
        ],
        'ad_cost_api' => [
            'gabe' => [
                'app_id' => env("META_GABE_ADS_AD_COST_APP_ID"),
                'app_secret' => env("META_GABE_ADS_AD_COST_APP_SECRET"),
                'token' => env("META_GABE_ADS_AD_COST_TOKEN")
            ],
            'wade' => [
                'app_id' => env("META_WADE_ADS_AD_COST_APP_ID"),
                'app_secret' => env("META_WADE_ADS_AD_COST_APP_SECRET"),
                'token' => env("META_WADE_ADS_AD_COST_TOKEN")
            ]
        ]
    ],

    'ads' => [
        'notification_emails' => env("ADS_NOTIFICATION_EMAILS"),
        'run_scheduled_jobs' => env("RUN_SCHEDULED_ADS_JOBS", true)
    ],

    'ping_post' => [
        'notification_emails' => env("PING_POST_NOTIFICATION_EMAILS"),
    ],

    'address_identification' => [
        'driver' => env('ADDRESS_IDENTIFICATION_DRIVER', 'dummy')
    ],

    'communication' => [
        'driver' => env('COMMUNICATION_DRIVER', 'dummy'),
    ],

    'lead_processing' => [
        'api_driver' => env('LEAD_PROCESSING_API_DRIVER', 'dummy')
    ],

    'sales_management' => [
        'api_driver' => env('SALES_MANAGEMENT_API_DRIVER', 'dummy')
    ],

    'user_management' => [
        'api_driver' => env('USER_MANAGEMENT_API_DRIVER', 'dummy')
    ],

    'filters' => [
        'api_driver' => env('FILTERS_API_DRIVER', 'dummy')
    ],

    'admin_integration' => [
        'base_url' => env('ADMIN_INTEGRATION_BASE_URL'),
        'client_secret' => env('ADMIN_INTEGRATION_CLIENT_SECRET')
    ],

    'legacy_admin' => [
        'auth_token' => env('LEGACY_ADMIN_AUTH_TOKEN')
    ],

    /**
     * The driver that is used for retrieving ip quality score
     *
     * 'dummy'            => A fake driver that can be used in dev.
     * 'ip_quality_score' => A driver that utilises the IP Quality service
     */
    'ip_quality_score' => [
        'base_url' => env('IP_QUALITY_SCORE_BASE_URL'),
        'api_key' => env('IP_QUALITY_SCORE_API_KEY'),
        'driver'   => env('IP_QUALITY_SCORE_DRIVER', 'dummy')
    ],

    'tiny_mce' => [
        'api_key' => env('TINY_MCE_API_KEY', 'no-api-key'),
        'storage_disk' => env('TIME_MCE_STORAGE_DISK', 'public') //disks public, cloud
    ],

    'jwt' => [
        'signing_keys' => [
            'auth_token' => env('AUTH_TOKEN_SINGING_KEY', 'b31d2c5f-c6bd-4514-bced-0950fb0cd7a0'),
            'consumer_token' => env('CONSUMER_TOKEN_SINGING_KEY', 'W4syd1tPMm4vQxPQKR29v3TRyFZVVZBppV3qX/NB6jQ=')
        ]
    ],

    'dashboard_client_api' => [
        'jwt' => [
            'signing_key' => env('DASHBOARD_CLIENT_API_JWT_SIGNING_KEY', 'secretkey'),
            'expire_in' => env('DASHBOARD_CLIENT_API_JWT_EXPIRE_IN', 28800) //default 8 hours
        ]
    ],

    'payment_gateways' => [
        'driver' => env('PAYMENT_GATEWAY_DRIVER'),
        'stripe' => [
            'gateway_mode' => env('STRIPE_LIVE', false),
            'api_version' => env('STRIPE_API_VERSION', '2017-08-15'),
            'api_key_secret_test' => env('STRIPE_API_KEY_SECRET_TEST', 'No-Test-secret-key-set'),
            'api_key_secret_live' => env('STRIPE_API_KEY_SECRET_LIVE', 'No-Live-secret-key-set'),
            'api_key_publishable_test' => env('STRIPE_API_KEY_PUBLISHABLE_TEST', 'No-Test-Publishable-key-set'),
            'api_key_publishable_live' => env('STRIPE_API_KEY_PUBLISHABLE_LIVE', 'No-Live-Publishable-key-set'),
            'webhook_signing_secret' => env('STRIPE_WEBHOOK_SIGNING_SECRET', 'No webhook signing secret set'),
        ]
    ],

    'company_metrics' => [
        'driver' => env('COMPANY_METRICS_DRIVER', 'spy-fu'),
        'similar_web' => [
            'base_url'  => env('SIMILAR_WEB_API_BASE_URL', 'https://api.similarweb.com/v4'),
            'api_key'   => env('SIMILAR_WEB_API_KEY'),
        ],
        'spy_fu' => [
            'base_url'  => env('SPY_FU_API_BASE_URL', 'https://www.spyfu.com/'),
            'api_key'   => env('SPY_FU_API_KEY'),
            'api_id'    => env('SPY_FU_API_ID')
        ],
        'semrush' => [
            'api_key' => env('SEMRUSH_API_KEY', ''),
        ]
    ],

    'tax_service' => [
        'driver' => env('TAX_SERVICE_DRIVER', 'dummy'),
    ],

    'marketing' => [
        'email' => [
            'driver' => env('EMAIL_MARKETING_DRIVER', 'mailchimp'),
            'mailchimp' => [
                'api_key' => env('EMAIL_MARKETING_API_KEY'),
                'server' => env('EMAIL_MARKETING_SERVER')
            ],
            'socket_labs' => [
                'api_key' => env('EMAIL_MARKETING_SOCKET_LABS_API_KEY'),
                'server' => env('EMAIL_MARKETING_SOCKET_LABS_SERVER'),
                'secret_key' => env('EMAIL_MARKETING_SOCKET_LABS_WEBHOOK_SECRET'),
            ],
            'socket_labs_alt' => [
                'api_key' => env('EMAIL_MARKETING_SOCKET_LABS_API_KEY_SECONDARY'),
                'server' => env('EMAIL_MARKETING_SOCKET_LABS_SERVER_SECONDARY'),
            ],
        ]
    ],

    'flow_builder' => [
        'url' => env('FLOW_BUILDER_URL', 'https://cdn.admin-sr.com/flow-builder/latest/cb.js')
    ],
    'flow_client' => [
        'url' => env('FLOW_CLIENT_URL', 'https://cdn.admin-sr.com/flow-client/latest/fc.js'),
        'default_safe_call_companies' => env('FLOW_BUILDER_DEFAULT_SAFE_CALL_IDS', ''),
    ],

    'scheduling' => [
        'base_api_url' => env('SCHEDULING_BASE_API_URL'),
        'client_secret' => env('SCHEDULING_CLIENT_SECRET')
    ],

    'mailslurp' => [
        'api_key' => env('MAILSLURP_API_KEY'),
    ],

    'docusign' => [
        'integration_key' => env('CONTRACT_PROVIDER_INTEGRATION_KEY'),
        'user_id' => env('CONTRACT_PROVIDER_USER_ID'),
        'private_key' => rtrim(base64_decode(env('CONTRACT_PROVIDER_PRIVATE_KEY', '')), '\n'),
        'account_id' => env('CONTRACT_PROVIDER_ACCOUNT_ID'),
    ],

    'mailbox' => [
        'api_driver' => env('MAILBOX_API_DRIVER', 'api'),
        'provider'   => env('MAILBOX_MAIL_PROVIDER', 'google'),
    ],
    'crm_delivery' => [
        'driver'      => env('CRM_DELIVERY_DRIVER', 'dummy'),
        'test_url'    => env('CRM_DELIVERY_TEST_URL', ''),
        'test_secret' => env('CRM_DELIVERY_TEST_SECRET', ''),
    ],

    'flow_engines' => [
        'url'   => env('FLOW_ENGINE_URL', 'https://dev-engines.fixr.com/'),
    ],

    'affiliates_portal_api' => [
        'token' => env('AFFILIATES_PORTAL_API_TOKEN', ''),
        'url' => env('AFFILIATES_PORTAL_API_URL', ''),
    ],

    'bundles' => [
        'api_version'      => env('BUNDLES_API_VERSION', 'v1'),
    ],

    'slack' => [
        'admin_system_bot_auth_token' => env('SLACK_ADMIN_SYSTEM_BOT_AUTH_TOKEN', ''),
        'company_registration_bot_auth_token' => env('SLACK_COMPANY_REGISTRATION_BOT_AUTH_TOKEN', ''),
        'registration_notification_channel_id' => env('SLACK_REGISTRATION_NOTIFICATION_CHANNEL_ID', ''),
    ],

    'salesintel' => [
        'api' => [
            'key' => env('SALES_INTEL_API_KEY'),
        ]
    ],

    /**
     * This is for local development only.
     * It sets whether classes that interact with live API should be mocked.
     * This ensures that you don't need the proper configuration set up to run the app locally.
     */
    'mock' => [
        'docusign' => env('MOCK_DOCUSIGN', false),
        'communication-contract' => env('MOCK_COMMUNICATION_CONTRACT', false),
        'twilio-communication' => env('MOCK_TWILIO_COMMUNICATION', false),
        'twilio-verify' => env('MOCK_TWILIO_VERIFY', false),
    ],

    'deepgram' => [
        'api_key' => env('DEEPGRAM_API_KEY'),
    ],

    'solar_classifier' => [
        'url' => env('SOLAR_CLASSIFIER_API_URL'),
        'api_key' => env('SOLAR_CLASSIFIER_API_KEY'),
    ]
];
