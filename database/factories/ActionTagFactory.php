<?php

namespace Database\Factories;

use App\Models\Action;
use App\Models\ActionCategory;
use App\Models\ActionTag;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<ActionTag>
 */
class ActionTagFactory extends Factory
{
    /**
     * @var string
     */
    protected $model = ActionTag::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition(): array
    {
        return [
            ActionTag::FIELD_ACTION_ID => Action::factory(),
            ActionTag::FIELD_USER_ID   => User::factory(),
        ];
    }
}

