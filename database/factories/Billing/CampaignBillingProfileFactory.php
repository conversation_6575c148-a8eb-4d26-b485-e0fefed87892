<?php

namespace Database\Factories\Billing;

use App\Abstracts\ResetUniqueFactory;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\CampaignBillingProfile;
use App\Models\Campaigns\CompanyCampaign;

class CampaignBillingProfileFactory extends ResetUniqueFactory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function getData(bool $reset = false): array
    {
        return [
            CampaignBillingProfile::FIELD_BILLING_PROFILE_ID  => BillingProfile::factory(),
            CampaignBillingProfile::FIELD_CAMPAIGN_ID => CompanyCampaign::factory(),
        ];
    }
}
