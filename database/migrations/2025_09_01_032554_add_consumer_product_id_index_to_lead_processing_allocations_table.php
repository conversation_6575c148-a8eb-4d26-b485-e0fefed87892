<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lead_processing_allocations', function (Blueprint $table) {
            $table->index('consumer_product_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lead_processing_allocations', function (Blueprint $table) {
            $table->dropIndex('lead_processing_allocations_consumer_product_id_index');
        });
    }
};
