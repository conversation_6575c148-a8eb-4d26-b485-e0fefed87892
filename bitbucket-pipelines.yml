options:
  size: 4x
  runtime:
    cloud:
      atlassian-ip-ranges: true
definitions:
  services:
    mysql:
      image: mysql:5.7
      environment:
        MYSQL_DATABASE: 'test_admin_20'
        MYSQL_ALLOW_EMPTY_PASSWORD: 'yes'
    docker:
      memory: 4096
  steps:
    - step: &create-deployment
        name: Create Deployment
        image: google/cloud-sdk:latest
        script:
          - echo "Creating Deployment"
          - echo $DEPLOYMENT_KEY_FILE | base64 -d > ~/.gcloud-api-key.json
          - export USE_GKE_GCLOUD_AUTH_PLUGIN=True
          - gcloud auth activate-service-account --key-file ~/.gcloud-api-key.json
          - gcloud config set project $DEPLOYMENT_PROJECT_ID
          - gcloud auth configure-docker --quiet
          - gcloud container clusters get-credentials $CLUSTER_NAME --zone=$CLUSTER_ZONE --project $DEPLOYMENT_PROJECT_ID
          - echo "Creating Deployment"
          - cp ./kubernetes/deployment-example.yml ~/deployment.yml
          - sed -i "s/sr-admin-example-deployment/sr-admin-$DEPLOYMENT_NAME-deployment/g" ~/deployment.yml
          - sed -i "s/example-project/$DEPLOYMENT_PROJECT_ID/g" ~/deployment.yml
          - sed -i "s/example-container:example-version/$INITIAL_CONTAINER/g" ~/deployment.yml
          - kubectl apply -f ~/deployment.yml
          - echo "Creating Queue Worker Deployment"
          - cp ./kubernetes/deployment-example.yml ~/queue-deployment.yml
          - sed -i "s/sr-admin-example-deployment/sr-admin-$DEPLOYMENT_NAME-queue-worker-deployment/g" ~/queue-deployment.yml
          - sed -i "s/example-project/$DEPLOYMENT_PROJECT_ID/g" ~/queue-deployment.yml
          - sed -i "s/example-container:example-version/$INITIAL_QUEUE_CONTAINER/g" ~/queue-deployment.yml
          - kubectl apply -f ~/queue-deployment.yml
          - echo "Creating front-end config"
          - cp ./kubernetes/frontend-example.yml ~/frontend.yml
          - sed -i "s/sr-admin-example-ingress-frontend-config/sr-admin-$DEPLOYMENT_NAME-ingress-frontend-config/g" ~/frontend.yml
          - kubectl apply -f ~/frontend.yml
          - echo "Creating Node Port"
          - cp ./kubernetes/node-port-example.yml ~/node-port.yml
          - sed -i "s/sr-admin-example-node-port/sr-admin-$DEPLOYMENT_NAME-node-port/g" ~/node-port.yml
          - sed -i "s/sr-admin-example-deployment/sr-admin-$DEPLOYMENT_NAME-deployment/g" ~/node-port.yml
          - kubectl apply -f ~/node-port.yml
          - echo "Create managed certificate"
          - cp ./kubernetes/managed-certificate-example.yml ~/managed-certificate.yml
          - sed -i "s/sr-admin-example-ssl-certificate/sr-admin-$DEPLOYMENT_NAME-ssl-certificate/g" ~/managed-certificate.yml
          - sed -i "s/example-domain/$SUBDOMAIN_NAME/g" ~/managed-certificate.yml
          - kubectl apply -f ~/managed-certificate.yml
          - echo "Creating Ingress & IP Address"
          - set +e
          - gcloud compute addresses create "sr-admin-$DEPLOYMENT_NAME-static-ip" --global
          - set -e
          - cp ./kubernetes/ingress-example.yml ~/ingress.yml
          - sed -i "s/sr-admin-example-static-ip/sr-admin-$DEPLOYMENT_NAME-static-ip/g" ~/ingress.yml
          - sed -i "s/sr-admin-example-ingress-frontend-config/sr-admin-$DEPLOYMENT_NAME-ingress-frontend-config/g" ~/ingress.yml
          - sed -i "s/sr-admin-example-ssl-certificate/sr-admin-$DEPLOYMENT_NAME-ssl-certificate/g" ~/ingress.yml
          - sed -i "s/sr-admin-example-ingress/sr-admin-$DEPLOYMENT_NAME-ingress/g" ~/ingress.yml
          - sed -i "s/sr-admin-example-node-port/sr-admin-$DEPLOYMENT_NAME-node-port/g" ~/ingress.yml
          - sed -i "s/example-domain/$SUBDOMAIN_NAME/g" ~/ingress.yml
          - kubectl apply -f ~/ingress.yml
    # A requirement in bitbucket pipelines to use deployment variables across multiple steps sadly.
    - step: &initialize-environment
        name: Initialize Environment
        clone:
          enabled: false
        artifacts:
          - .deployment-env
        script:
          - echo export BITBUCKET_SSH_KEY_FILE="$(base64 -w 0 < ${BITBUCKET_SSH_KEY_FILE})" | tee -a .deployment-env
          - echo export APP_NAME=${APP_NAME} | tee -a .deployment-env
          - echo export APP_ENV=${APP_ENV} | tee -a .deployment-env
          - echo export MIX_APP_ENV=${APP_ENV} | tee -a .deployment-env
          - echo export APP_KEY=${APP_KEY} | tee -a .deployment-env
          - echo export APP_DEBUG=${APP_DEBUG} | tee -a .deployment-env
          - echo export APP_URL=${APP_URL} | tee -a .deployment-env
          - echo export DB_CONNECTION=${DB_CONNECTION} | tee -a .deployment-env
          - echo export DB_HOST=${DB_HOST} | tee -a .deployment-env
          - echo export DB_PORT=${DB_PORT} | tee -a .deployment-env
          - echo export DB_DATABASE=${DB_DATABASE} | tee -a .deployment-env
          - echo export DB_USERNAME=${DB_USERNAME} | tee -a .deployment-env
          - echo export DB_PASSWORD=${DB_PASSWORD} | tee -a .deployment-env
          - echo export REPLICA_DB_CONNECTION=${DB_CONNECTION} | tee -a .deployment-env
          - echo export REPLICA_DB_HOST=${REPLICA_DB_HOST} | tee -a .deployment-env
          - echo export REPLICA_TWO_DB_HOST=${REPLICA_TWO_DB_HOST} | tee -a .deployment-env
          - echo export REPLICA_DB_PORT=${REPLICA_DB_PORT} | tee -a .deployment-env
          - echo export REPLICA_DB_DATABASE=${REPLICA_DB_DATABASE} | tee -a .deployment-env
          - echo export REPLICA_DB_USERNAME=${REPLICA_DB_USERNAME} | tee -a .deployment-env
          - echo export REPLICA_DB_PASSWORD=${REPLICA_DB_PASSWORD} | tee -a .deployment-env
          - echo export READONLY_DB_CONNECTION=${READONLY_DB_CONNECTION} | tee -a .deployment-env
          - echo export READONLY_DB_HOST=${READONLY_DB_HOST} | tee -a .deployment-env
          - echo export READONLY_DB_PORT=${READONLY_DB_PORT} | tee -a .deployment-env
          - echo export READONLY_DB_DATABASE=${READONLY_DB_DATABASE} | tee -a .deployment-env
          - echo export READONLY_DB_USERNAME=${READONLY_DB_USERNAME} | tee -a .deployment-env
          - echo export READONLY_DB_PASSWORD=${READONLY_DB_PASSWORD} | tee -a .deployment-env
          - echo export QUEUE_CONNECTION=${QUEUE_CONNECTION} | tee -a .deployment-env
          - echo export REDIS_CLIENT=${REDIS_CLIENT} | tee -a .deployment-env
          - echo export REDIS_HOST=${REDIS_HOST} | tee -a .deployment-env
          - echo export REDIS_QUEUE_HOST=${REDIS_QUEUE_HOST} | tee -a .deployment-env
          - echo export REDIS_QUEUE_PORT=${REDIS_QUEUE_PORT} | tee -a .deployment-env
          - echo export SESSION_DRIVER=${SESSION_DRIVER} | tee -a .deployment-env
          - echo export PROJECT_ID=${PROJECT_ID} | tee -a .deployment-env
          - echo export DEPLOYMENT_NAME=${DEPLOYMENT_NAME} | tee -a .deployment-env
          - echo export CLUSTER_NAME=${CLUSTER_NAME} | tee -a .deployment-env
          - echo export KEY_FILE=${KEY_FILE} | tee -a .deployment-env
          - echo export DEFAULT_ZONE=${DEFAULT_ZONE} | tee -a .deployment-env
          - echo export SENTRY_DSN=${SENTRY_DSN} | tee -a .deployment-env
          - echo export MIX_SENTRY_DSN=${SENTRY_DSN} | tee -a .deployment-env
          - echo export SENTRY_TRACE_QUEUE_ENABLED=${SENTRY_TRACE_QUEUE_ENABLED} | tee -a .deployment-env
          - echo export SENTRY_SEND_DEFAULT_PII=${SENTRY_SEND_DEFAULT_PII} | tee -a .deployment-env
          - echo export SENTRY_TRACES_SAMPLE_RATE=${SENTRY_TRACES_SAMPLE_RATE} | tee -a .deployment-env
          - echo export LEAD_VERIFICATION_DRIVER=${LEAD_VERIFICATION_DRIVER} | tee -a .deployment-env
          - echo export WHITEPAGES_IDENTITY_VERIFICATION_KEY=${WHITEPAGES_IDENTITY_VERIFICATION_KEY} | tee -a .deployment-env
          - echo export WHITEPAGES_IDENTITY_VERIFICATION_URL=${WHITEPAGES_IDENTITY_VERIFICATION_URL} | tee -a .deployment-env
          - echo export TWILIO_ML_SID=${TWILIO_ML_SID} | tee -a .deployment-env
          - echo export TWILIO_SID=${TWILIO_SID} | tee -a .deployment-env
          - echo export TWILIO_TOKEN=${TWILIO_TOKEN} | tee -a .deployment-env
          - echo export TWILIO_REVIEW_REQUESTS=${TWILIO_REVIEW_REQUESTS} | tee -a .deployment-env
          - echo export LEAD_COMMUNICATION_DRIVER=${COMMUNICATION_DRIVER} | tee -a .deployment-env
          - echo export COMMUNICATION_DRIVER=${COMMUNICATION_DRIVER} | tee -a .deployment-env
          - echo export PUSHER_APP_ID=${PUSHER_APP_ID} | tee -a .deployment-env
          - echo export PUSHER_APP_KEY=${PUSHER_APP_KEY} | tee -a .deployment-env
          - echo export PUSHER_APP_SECRET=${PUSHER_APP_SECRET} | tee -a .deployment-env
          - echo export PUSHER_APP_CLUSTER=${PUSHER_APP_CLUSTER} | tee -a .deployment-env
          - echo export BROADCAST_DRIVER=${BROADCAST_DRIVER} | tee -a .deployment-env
          - echo export CACHE_DRIVER=${CACHE_DRIVER} | tee -a .deployment-env
          - echo export LEAD_PROCESSING_API_DRIVER=${LEAD_PROCESSING_API_DRIVER} | tee -a .deployment-env
          - echo export SALES_MANAGEMENT_API_DRIVER=${SALES_MANAGEMENT_API_DRIVER} | tee -a .deployment-env
          - echo export USER_MANAGEMENT_API_DRIVER=${USER_MANAGEMENT_API_DRIVER} | tee -a .deployment-env
          - echo export ADMIN_INTEGRATION_CLIENT_SECRET=${ADMIN_INTEGRATION_CLIENT_SECRET} | tee -a .deployment-env
          - echo export ADMIN_INTEGRATION_BASE_URL=${ADMIN_INTEGRATION_BASE_URL} | tee -a .deployment-env
          - echo export MAIL_MAILER=${MAIL_MAILER} | tee -a .deployment-env
          - echo export MAIL_HOST=${MAIL_HOST} | tee -a .deployment-env
          - echo export MAIL_PORT=${MAIL_PORT} | tee -a .deployment-env
          - echo export MAIL_USERNAME=${MAIL_USERNAME} | tee -a .deployment-env
          - echo export MAIL_PASSWORD=${MAIL_PASSWORD} | tee -a .deployment-env
          - echo export MAIL_USERNAME_SECONDARY=${MAIL_USERNAME_SECONDARY} | tee -a .deployment-env
          - echo export MAIL_PASSWORD_SECONDARY=${MAIL_PASSWORD_SECONDARY} | tee -a .deployment-env
          - echo export MAIL_USERNAME_TERTIARY=${MAIL_USERNAME_TERTIARY} | tee -a .deployment-env
          - echo export MAIL_PASSWORD_TERTIARY=${MAIL_PASSWORD_TERTIARY} | tee -a .deployment-env
          - echo export MAIL_FROM_ADDRESS=${MAIL_FROM_ADDRESS} | tee -a .deployment-env
          - echo export MAIL_FROM_NAME=${MAIL_FROM_NAME} | tee -a .deployment-env
          - echo export SOLAR_MAIL_FROM_NAME=${SOLAR_MAIL_FROM_NAME} | tee -a .deployment-env
          - echo export ROOFING_MAIL_FROM_NAME=${ROOFING_MAIL_FROM_NAME} | tee -a .deployment-env
          - echo export SOLAR_MAIL_FROM_ADDRESS=${SOLAR_MAIL_FROM_ADDRESS} | tee -a .deployment-env
          - echo export ROOFING_MAIL_FROM_ADDRESS=${ROOFING_MAIL_FROM_ADDRESS} | tee -a .deployment-env
          - echo export LEGACY_ADMIN_AUTH_TOKEN=${LEGACY_ADMIN_AUTH_TOKEN} | tee -a .deployment-env
          - echo export GOOGLE_STATIC_MAPS_API_KEY=${GOOGLE_STATIC_MAPS_API_KEY} | tee -a .deployment-env
          - echo export GOOGLE_MAPS_GEOCODING_API_KEY=${GOOGLE_MAPS_GEOCODING_API_KEY} | tee -a .deployment-env
          - echo export GOOGLE_CLOUD_KEY_FILE=${GOOGLE_CLOUD_KEY_FILE} | tee -a .deployment-env
          - echo export GOOGLE_STORAGE_INVOICES_BUCKET=${GOOGLE_STORAGE_INVOICES_BUCKET} | tee -a .deployment-env
          - echo export PAYMENT_GATEWAY_DRIVER=${PAYMENT_GATEWAY_DRIVER} | tee -a .deployment-env
          - echo export STRIPE_WEBHOOK_SIGNING_SECRET=${STRIPE_WEBHOOK_SIGNING_SECRET} | tee -a .deployment-env
          - echo export GOOGLE_GMAIL_CLIENT_ID=${GOOGLE_GMAIL_CLIENT_ID} | tee -a .deployment-env
          - echo export GOOGLE_GMAIL_CLIENT_SECRET=${GOOGLE_GMAIL_CLIENT_SECRET} | tee -a .deployment-env
          - echo export GOOGLE_GMAIL_EMAIL_LISTENER_TOPIC=${GOOGLE_GMAIL_EMAIL_LISTENER_TOPIC} | tee -a .deployment-env
          - echo export MAILBOX_MAIL_PROVIDER=${MAILBOX_MAIL_PROVIDER} | tee -a .deployment-env
          - echo export MAILBOX_API_DRIVER=${MAILBOX_API_DRIVER} | tee -a .deployment-env
          - echo export OUTGOING_COMMUNICATION_TEST_MODE=${OUTGOING_COMMUNICATION_TEST_MODE} | tee -a .deployment-env
          - echo export MIX_LEGACY_ADMIN_BASE_URL=${MIX_LEGACY_ADMIN_BASE_URL} | tee -a .deployment-env
          - echo export WWW_SOLARREVIEWS_DOMAIN=${WWW_SOLARREVIEWS_DOMAIN} | tee -a .deployment-env
          - echo export GOOGLE_LOGGING_PROJECT_ID=${GOOGLE_LOGGING_PROJECT_ID} | tee -a .deployment-env
          - echo export GOOGLE_PUBSUB_PROJECT_ID=${GOOGLE_PUBSUB_PROJECT_ID} | tee -a .deployment-env
          - echo export GOOGLE_PUBSUB_TOPIC=${GOOGLE_PUBSUB_TOPIC} | tee -a .deployment-env
          - echo export GOOGLE_STORAGE_PROJECT_ID=${GOOGLE_STORAGE_PROJECT_ID} | tee -a .deployment-env
          - echo export STACKDRIVER_ENABLED=${STACKDRIVER_ENABLED} | tee -a .deployment-env
          - echo export GOOGLE_STORAGE_EMAIL_TEMPLATE_IMAGES_BUCKET=${GOOGLE_STORAGE_EMAIL_TEMPLATE_IMAGES_BUCKET} | tee -a .deployment-env
          - echo export GOOGLE_ADS_DEVELOPER_TOKEN=${GOOGLE_ADS_DEVELOPER_TOKEN} | tee -a .deployment-env
          - echo export GOOGLE_ADS_CLIENT_ID=${GOOGLE_ADS_CLIENT_ID} | tee -a .deployment-env
          - echo export GOOGLE_ADS_CLIENT_SECRET=${GOOGLE_ADS_CLIENT_SECRET} | tee -a .deployment-env
          - echo export GOOGLE_ADS_REFRESH_TOKEN=${GOOGLE_ADS_REFRESH_TOKEN} | tee -a .deployment-env
          - echo export GOOGLE_ADS_GEOTARGETS_CSV_URL=${GOOGLE_ADS_GEOTARGETS_CSV_URL} | tee -a .deployment-env
          - echo export ADVERTISING_DRIVER=${ADVERTISING_DRIVER} | tee -a .deployment-env
          - echo export CLIENT_JWT_SIGNING_KEY=${CLIENT_JWT_SIGNING_KEY} | tee -a .deployment-env
          - echo export GOOGLE_MAPS_PLACES_API_URI=${GOOGLE_MAPS_PLACES_API_URI} | tee -a .deployment-env
          - echo export ADDRESS_IDENTIFICATION_DRIVER=${ADDRESS_IDENTIFICATION_DRIVER} | tee -a .deployment-env
          - echo export GOOGLE_ADS_IMPERSONATED_EMAIL=${GOOGLE_ADS_IMPERSONATED_EMAIL} | tee -a .deployment-env
          - echo export GOOGLE_ADS_AUTH_DRIVER=${GOOGLE_ADS_AUTH_DRIVER} | tee -a .deployment-env
          - echo export GOOGLE_OAUTH_SERVICE_ACCOUNT=${GOOGLE_OAUTH_SERVICE_ACCOUNT} | tee -a .deployment-env
          - echo export IP_QUALITY_SCORE_BASE_URL=${IP_QUALITY_SCORE_BASE_URL} | tee -a .deployment-env
          - echo export IP_QUALITY_SCORE_API_KEY=${IP_QUALITY_SCORE_API_KEY} | tee -a .deployment-env
          - echo export IP_QUALITY_SCORE_DRIVER=${IP_QUALITY_SCORE_DRIVER} | tee -a .deployment-env
          - echo export DATABASE_CONNECTION_RETRY_AFTER=${DATABASE_CONNECTION_RETRY_AFTER} | tee -a .deployment-env
          - echo export MICROSOFT_ADS_CLIENT_ID=${MICROSOFT_ADS_CLIENT_ID} | tee -a .deployment-env
          - echo export MICROSOFT_ADS_OAUTH_REDIRECT_URI=${MICROSOFT_ADS_OAUTH_REDIRECT_URI} | tee -a .deployment-env
          - echo export MICROSOFT_ADS_CUSTOMER_ID=${MICROSOFT_ADS_CUSTOMER_ID} | tee -a .deployment-env
          - echo export MICROSOFT_ADS_OFFLINE_CONVERSION_GOAL_NAME=${MICROSOFT_ADS_OFFLINE_CONVERSION_GOAL_NAME} | tee -a .deployment-env
          - echo export MICROSOFT_ADS_DEVELOPER_TOKEN=${MICROSOFT_ADS_DEVELOPER_TOKEN} | tee -a .deployment-env
          - echo export GOOGLE_ADS_CONVERSION_ACTION_NAME=${GOOGLE_ADS_CONVERSION_ACTION_NAME} | tee -a .deployment-env
          - echo export MICROSOFT_ADS_CLIENT_SECRET=${MICROSOFT_ADS_CLIENT_SECRET} | tee -a .deployment-env
          - echo export ADS_NOTIFICATION_EMAILS=${ADS_NOTIFICATION_EMAILS} | tee -a .deployment-env
          - echo export PING_POST_NOTIFICATION_EMAILS=${PING_POST_NOTIFICATION_EMAILS} | tee -a .deployment-env
          - echo export TINY_MCE_API_KEY=${TINY_MCE_API_KEY} | tee -a .deployment-env
          - echo export TIME_MCE_STORAGE_DISK=${TIME_MCE_STORAGE_DISK} | tee -a .deployment-env
          - echo export GOOGLE_STORAGE_TINYMCE_FILES_BUCKET=${GOOGLE_STORAGE_TINYMCE_FILES_BUCKET} | tee -a .deployment-env
          - echo export GOOGLE_STORAGE_TINYMCE_FILE_URL=${GOOGLE_STORAGE_TINYMCE_FILE_URL} | tee -a .deployment-env
          - echo export GOOGLE_STORAGE_MAILBOX_BUCKET=${GOOGLE_STORAGE_MAILBOX_BUCKET} | tee -a .deployment-env
          - echo export GOOGLE_STORAGE_MAILBOX_CDN_URL=${GOOGLE_STORAGE_MAILBOX_CDN_URL} | tee -a .deployment-env
          - echo export HUNTER_PRIORITY_CALCULATION_UNSOLD_DAYS=${HUNTER_PRIORITY_CALCULATION_UNSOLD_DAYS} | tee -a .deployment-env
          - echo export CONSUMER_PRODUCT_VERIFICATION_DRIVER=${CONSUMER_PRODUCT_VERIFICATION_DRIVER} | tee -a .deployment-env
          - echo export FIXR_DOMAIN=${FIXR_DOMAIN} | tee -a .deployment-env
          - echo export FIXR_ORIGIN_DOMAIN=${FIXR_ORIGIN_DOMAIN} | tee -a .deployment-env
          - echo export AUTH_TOKEN_SINGING_KEY=${AUTH_TOKEN_SINGING_KEY} | tee -a .deployment-env
          - echo export TWILIO_FROM_PHONE_NUMBER=${TWILIO_FROM_PHONE_NUMBER} | tee -a .deployment-env
          - echo export TWILIO_CONSUMER_VERIFICATION_FROM_PHONE=${TWILIO_CONSUMER_VERIFICATION_FROM_PHONE} | tee -a .deployment-env
          - echo export TWILIO_RECYCLED_LEADS_FROM_PHONE=${TWILIO_RECYCLED_LEADS_FROM_PHONE} | tee -a .deployment-env
          - echo export META_ADS_APP_ID=${META_ADS_APP_ID} | tee -a .deployment-env
          - echo export META_ADS_APP_SECRET=${META_ADS_APP_SECRET} | tee -a .deployment-env
          - echo export META_ADS_BUSINESS_ID=${META_ADS_BUSINESS_ID} | tee -a .deployment-env
          - echo export META_ADS_ADMIN_SYSTEM_USER_TOKEN=${META_ADS_ADMIN_SYSTEM_USER_TOKEN} | tee -a .deployment-env
          - echo export COMPANY_DISCOVERY_GOOGLE_PLACES_API_KEY=${COMPANY_DISCOVERY_GOOGLE_PLACES_API_KEY} | tee -a .deployment-env
          - echo export STRIPE_LIVE=${STRIPE_LIVE} | tee -a .deployment-env
          - echo export STRIPE_API_VERSION=${STRIPE_API_VERSION} | tee -a .deployment-env
          - echo export STRIPE_API_KEY_SECRET_TEST=${STRIPE_API_KEY_SECRET_TEST} | tee -a .deployment-env
          - echo export STRIPE_API_KEY_SECRET_LIVE=${STRIPE_API_KEY_SECRET_LIVE} | tee -a .deployment-env
          - echo export STRIPE_API_KEY_PUBLISHABLE_TEST=${STRIPE_API_KEY_PUBLISHABLE_TEST} | tee -a .deployment-env
          - echo export STRIPE_API_KEY_PUBLISHABLE_LIVE=${STRIPE_API_KEY_PUBLISHABLE_LIVE} | tee -a .deployment-env
          - echo export LOG_SLACK_WEBHOOK_URL=${LOG_SLACK_WEBHOOK_URL} | tee -a .deployment-env
          - echo export DASHBOARD_CLIENT_API_JWT_SIGNING_KEY=${DASHBOARD_CLIENT_API_JWT_SIGNING_KEY} | tee -a .deployment-env
          - echo export DASHBOARD_CLIENT_API_JWT_EXPIRE_IN=${DASHBOARD_CLIENT_API_JWT_EXPIRE_IN} | tee -a .deployment-env
          - echo export LEAD_MINIMUM_ELECTRIC_SPEND=${LEAD_MINIMUM_ELECTRIC_SPEND} | tee -a .deployment-env
          - echo export LEAD_REJECTION_PERCENTAGE_THRESHOLD=${LEAD_REJECTION_PERCENTAGE_THRESHOLD} | tee -a .deployment-env
          - echo export LEAD_CRM_REJECTION_PERCENTAGE_THRESHOLD=${LEAD_CRM_REJECTION_PERCENTAGE_THRESHOLD} | tee -a .deployment-env
          - echo export APPT_MINIMUM_ELECTRIC_SPEND=${APPT_MINIMUM_ELECTRIC_SPEND} | tee -a .deployment-env
          - echo export APPT_REJECTION_PERCENTAGE_THRESHOLD=${APPT_REJECTION_PERCENTAGE_THRESHOLD} | tee -a .deployment-env
          - echo export APPT_CRM_REJECTION_PERCENTAGE_THRESHOLD=${APPT_CRM_REJECTION_PERCENTAGE_THRESHOLD} | tee -a .deployment-env
          - echo export MAX_CHARGE_ATTEMPTS=${MAX_CHARGE_ATTEMPTS} | tee -a .deployment-env
          - echo export APPT_OFFERING_AVAILABLE_DURATION_MIN=${APPT_OFFERING_AVAILABLE_DURATION_MIN} | tee -a .deployment-env
          - echo export APPTS_SOLARREVIEWS_DOMAIN=${APPTS_SOLARREVIEWS_DOMAIN} | tee -a .deployment-env
          - echo export APPT_MAX_OFFERING_ATTEMPTS=${APPT_MAX_OFFERING_ATTEMPTS} | tee -a .deployment-env
          - echo export APPT_REJECTION_WINDOW_DURATION_HOURS=${APPT_REJECTION_WINDOW_DURATION_HOURS} | tee -a .deployment-env
          - echo export BRS_DRIVER=${BRS_DRIVER} | tee -a .deployment-env
          - echo export APPT_CONSUMER_ADVANCE_NOTICE_HOURS=${APPT_CONSUMER_ADVANCE_NOTICE_HOURS} | tee -a .deployment-env
          - echo export PRODUCT_PRICING_DRIVER=${PRODUCT_PRICING_DRIVER} | tee -a .deployment-env
          - echo export LEAD_REJECTION_WINDOW_DURATION_HOURS=${LEAD_REJECTION_WINDOW_DURATION_HOURS} | tee -a .deployment-env
          - echo export APPT_LOG_BRS_QUERIES=${APPT_LOG_BRS_QUERIES} | tee -a .deployment-env
          - echo export LOG_JOB_EVENTS=${LOG_JOB_EVENTS} | tee -a .deployment-env
          - echo export GOOGLE_FIRESTORE_PROJECT_ID=${GOOGLE_FIRESTORE_PROJECT_ID} | tee -a .deployment-env
          - echo export GOOGLE_FIRESTORE_KEY_FILE=${GOOGLE_FIRESTORE_KEY_FILE} | tee -a .deployment-env
          - echo export GOOGLE_FIRESTORE_COLLECTION_PREFIX=${GOOGLE_FIRESTORE_COLLECTION_PREFIX} | tee -a .deployment-env
          - echo export FLOW_BUILDER_IMAGE_BUCKET=${FLOW_BUILDER_IMAGE_BUCKET} | tee -a .deployment-env
          - echo export FLOW_BUILDER_CDN_URL=${FLOW_BUILDER_CDN_URL} | tee -a .deployment-env
          - echo export FLOW_BUILDER_URL=${FLOW_BUILDER_URL} | tee -a .deployment-env
          - echo export FLOW_PROXY_URL=${FLOW_PROXY_URL} | tee -a .deployment-env
          - echo export FLOW_PROXY_SECRET=${FLOW_PROXY_SECRET} | tee -a .deployment-env
          - echo export FLOW_CLIENT_URL=${FLOW_CLIENT_URL} | tee -a .deployment-env
          - echo export SCHEDULING_BASE_API_URL=${SCHEDULING_BASE_API_URL} | tee -a .deployment-env
          - echo export SCHEDULING_CLIENT_SECRET=${SCHEDULING_CLIENT_SECRET} | tee -a .deployment-env
          - echo export ALLOCATE_DIRECT_LEADS_WITHOUT_TIMEZONE_OPEN_DELAY=${ALLOCATE_DIRECT_LEADS_WITHOUT_TIMEZONE_OPEN_DELAY} | tee -a .deployment-env
          - echo export DATABASE_QUEUE_AFTER_COMMIT=${DATABASE_QUEUE_AFTER_COMMIT} | tee -a .deployment-env
          - echo export APPT_NEXT_DELIVERY_ATTEMPT_BUFFER_MINUTES=${APPT_NEXT_DELIVERY_ATTEMPT_BUFFER_MINUTES} | tee -a .deployment-env
          - echo export APPT_MAX_DELIVERY_ATTEMPTS=${APPT_MAX_DELIVERY_ATTEMPTS} | tee -a .deployment-env
          - echo export GOOGLE_ADS_LOGIN_CUSTOMER_ID=${GOOGLE_ADS_LOGIN_CUSTOMER_ID} | tee -a .deployment-env
          - echo export URL_SIGNER_SIGNATURE_KEY=${URL_SIGNER_SIGNATURE_KEY} | tee -a .deployment-env
          - echo export OPPORTUNITY_NOTIFICATIONS_CRON=${OPPORTUNITY_NOTIFICATIONS_CRON} | tee -a .deployment-env
          - echo export GOOGLE_DEBUG_MONITORING_LOGS=${GOOGLE_DEBUG_MONITORING_LOGS} | tee -a .deployment-env
          - echo export APPT_SELL_APPT_AS_LEAD_BUFFER_AFTER_MIN=${APPT_SELL_APPT_AS_LEAD_BUFFER_AFTER_MIN} | tee -a .deployment-env
          - echo export COMPOSER_AUTH=${COMPOSER_AUTH} | tee -a .deployment-env
          - echo export FILTERS_API_DRIVER=${FILTERS_API_DRIVER} | tee -a .deployment-env
          - echo export FAILED_JOB_EMAILS=${FAILED_JOB_EMAILS} | tee -a .deployment-env
          - echo export RUN_SCHEDULED_ADS_JOBS=${RUN_SCHEDULED_ADS_JOBS} | tee -a .deployment-env
          - echo export MAILSLURP_API_KEY=${MAILSLURP_API_KEY} | tee -a .deployment-env
          - echo export SOLAR_REVIEWS_FRONTEND_DOMAIN=${SOLAR_REVIEWS_FRONTEND_DOMAIN} | tee -a .deployment-env
          - echo export FLOW_BUILDER_DEFAULT_SAFE_CALL_IDS=${FLOW_BUILDER_DEFAULT_SAFE_CALL_IDS} | tee -a .deployment-env
          - echo export ARE_UTILITY_FILTERS_ACTIVE=${ARE_UTILITY_FILTERS_ACTIVE} | tee -a .deployment-env
          - echo export COMPANY_SEARCH_FILTER_PURCHASING_FROM_COMPETITOR_QUERY_STRATEGY=${COMPANY_SEARCH_FILTER_PURCHASING_FROM_COMPETITOR_QUERY_STRATEGY} | tee -a .deployment-env
          - echo export DELIVERY_FAILURE_NOTIFICATION_EMAILS=${DELIVERY_FAILURE_NOTIFICATION_EMAILS} | tee -a .deployment-env
          - echo export DROPBOX_API_KEY=${DROPBOX_API_KEY} | tee -a .deployment-env
          - echo export MIX_DROPBOX_ACCOUNT_ID=${MIX_DROPBOX_ACCOUNT_ID} | tee -a .deployment-env
          - echo export COMPANY_METRICS_DRIVER=${COMPANY_METRICS_DRIVER} | tee -a .deployment-env
          - echo export SIMILAR_WEB_API_BASE_URL=${SIMILAR_WEB_API_BASE_URL} | tee -a .deployment-env
          - echo export SIMILAR_WEB_API_KEY=${SIMILAR_WEB_API_KEY} | tee -a .deployment-env
          - echo export SPY_FU_API_BASE_URL=${SPY_FU_API_BASE_URL} | tee -a .deployment-env
          - echo export SPY_FU_API_KEY=${SPY_FU_API_KEY} | tee -a .deployment-env
          - echo export SPY_FU_API_ID=${SPY_FU_API_ID} | tee -a .deployment-env
          - echo export GOOGLE_STORAGE_CONTRACT_BUCKET=${GOOGLE_STORAGE_CONTRACT_BUCKET} | tee -a .deployment-env
          - echo export GOOGLE_STORAGE_CONTRACT_URL=${GOOGLE_STORAGE_CONTRACT_URL} | tee -a .deployment-env
          - echo export LARGE_ACCOUNT_REVENUE_THRESHOLD=${LARGE_ACCOUNT_REVENUE_THRESHOLD} | tee -a .deployment-env
          - echo export STAGING_TESTING_PUBSUB_TOPIC=${STAGING_TESTING_PUBSUB_TOPIC} | tee -a .deployment-env
          - echo export FUTURE_ALLOCATION_TESTING_INDUSTRY_ID=${FUTURE_ALLOCATION_TESTING_INDUSTRY_ID} | tee -a .deployment-env
          - echo export TWILIO_VERIFY_SERVICE_SID=${TWILIO_VERIFY_SERVICE_SID} | tee -a .deployment-env
          - echo export META_ADS_AUTOMATION_SYSTEM_USER_NAME=${META_ADS_AUTOMATION_SYSTEM_USER_NAME} | tee -a .deployment-env
          - echo export OMIT_LOW_NEVER_EXCEED_BUDGET=${OMIT_LOW_NEVER_EXCEED_BUDGET} | tee -a .deployment-env
          - echo export META_WADE_ADS_APP_ID=${META_WADE_ADS_APP_ID} | tee -a .deployment-env
          - echo export META_WADE_ADS_APP_SECRET=${META_WADE_ADS_APP_SECRET} | tee -a .deployment-env
          - echo export META_WADE_ADS_BUSINESS_ID=${META_WADE_ADS_BUSINESS_ID} | tee -a .deployment-env
          - echo export META_WADE_ADS_ADMIN_SYSTEM_USER_TOKEN=${META_WADE_ADS_ADMIN_SYSTEM_USER_TOKEN} | tee -a .deployment-env
          - echo export META_WADE_ADS_PIXEL_ID=${META_WADE_ADS_PIXEL_ID} | tee -a .deployment-env
          - echo export META_GABE_ADS_AD_COST_APP_ID=${META_GABE_ADS_AD_COST_APP_ID} | tee -a .deployment-env
          - echo export META_GABE_ADS_AD_COST_APP_SECRET=${META_GABE_ADS_AD_COST_APP_SECRET} | tee -a .deployment-env
          - echo export META_GABE_ADS_AD_COST_TOKEN=${META_GABE_ADS_AD_COST_TOKEN} | tee -a .deployment-env
          - echo export META_WADE_ADS_AD_COST_APP_ID=${META_WADE_ADS_AD_COST_APP_ID} | tee -a .deployment-env
          - echo export META_WADE_ADS_AD_COST_APP_SECRET=${META_WADE_ADS_AD_COST_APP_SECRET} | tee -a .deployment-env
          - echo export META_WADE_ADS_AD_COST_TOKEN=${META_WADE_ADS_AD_COST_TOKEN} | tee -a .deployment-env
          - echo export WATCHDOG_TWO_SERVER_URL=${WATCHDOG_TWO_SERVER_URL} | tee -a .deployment-env
          - echo export WATCHDOG_TWO_PERSONAL_ACCESS_TOKEN=${WATCHDOG_TWO_PERSONAL_ACCESS_TOKEN} | tee -a .deployment-env
          - echo export PRIVACY_ACCESS_TOKEN=${PRIVACY_ACCESS_TOKEN} | tee -a .deployment-env
          - echo export SOLAR_ESTIMATE_DOMAIN=${SOLAR_ESTIMATE_DOMAIN} | tee -a .deployment-env
          - echo export AFFILIATES_PORTAL_API_TOKEN=${AFFILIATES_PORTAL_API_TOKEN} | tee -a .deployment-env
          - echo export AFFILIATES_PORTAL_API_URL=${AFFILIATES_PORTAL_API_URL} | tee -a .deployment-env
          - echo export FLOW_ENGINE_URL=${FLOW_ENGINE_URL} | tee -a .deployment-env
          - echo export EMAIL_MARKETING_API_KEY=${EMAIL_MARKETING_API_KEY} | tee -a .deployment-env
          - echo export EMAIL_MARKETING_SERVER=${EMAIL_MARKETING_SERVER} | tee -a .deployment-env
          - echo export EMAIL_MARKETING_DRIVER=${EMAIL_MARKETING_DRIVER} | tee -a .deployment-env
          - echo export DIRECT_LEAD_ENABLED_INDUSTRY_IDS=${DIRECT_LEAD_ENABLED_INDUSTRY_IDS} | tee -a .deployment-env
          - echo export CONTRACT_PROVIDER_INTEGRATION_KEY=${CONTRACT_PROVIDER_INTEGRATION_KEY} | tee -a .deployment-env
          - echo export CONTRACT_PROVIDER_USER_ID=${CONTRACT_PROVIDER_USER_ID} | tee -a .deployment-env
          - echo export CONTRACT_PROVIDER_ACCOUNT_ID=${CONTRACT_PROVIDER_ACCOUNT_ID} | tee -a .deployment-env
          - echo export CONTRACT_PROVIDER_PRIVATE_KEY=${CONTRACT_PROVIDER_PRIVATE_KEY} | tee -a .deployment-env
          - echo export EMAIL_MARKETING_SOCKET_LABS_SERVER=${EMAIL_MARKETING_SOCKET_LABS_SERVER} | tee -a .deployment-env
          - echo export EMAIL_MARKETING_SOCKET_LABS_API_KEY=${EMAIL_MARKETING_SOCKET_LABS_API_KEY} | tee -a .deployment-env
          - echo export SLACK_ADMIN_SYSTEM_BOT_AUTH_TOKEN=${SLACK_ADMIN_SYSTEM_BOT_AUTH_TOKEN} | tee -a .deployment-env
          - echo export SLACK_COMPANY_REGISTRATION_BOT_AUTH_TOKEN=${SLACK_COMPANY_REGISTRATION_BOT_AUTH_TOKEN} | tee -a .deployment-env
          - echo export SLACK_REGISTRATION_NOTIFICATION_CHANNEL_ID=${SLACK_REGISTRATION_NOTIFICATION_CHANNEL_ID} | tee -a .deployment-env
          - echo export SALES_INTEL_API_KEY=${SALES_INTEL_API_KEY} | tee -a .deployment-env
          - echo export GOOGLE_STORAGE_BASE_URL=${GOOGLE_STORAGE_BASE_URL} | tee -a .deployment-env
          - echo export REVIEW_ATTACHMENTS_BUCKET=${REVIEW_ATTACHMENTS_BUCKET} | tee -a .deployment-env
          - echo export EMAIL_MARKETING_SOCKET_LABS_WEBHOOK_SECRET=${EMAIL_MARKETING_SOCKET_LABS_WEBHOOK_SECRET} | tee -a .deployment-env
          - echo export DEEPGRAM_API_KEY=${DEEPGRAM_API_KEY} | tee -a .deployment-env
          - echo export EMAIL_MARKETING_SOCKET_LABS_API_KEY_SECONDARY=${EMAIL_MARKETING_SOCKET_LABS_API_KEY_SECONDARY} | tee -a .deployment-env
          - echo export EMAIL_MARKETING_SOCKET_LABS_SERVER_SECONDARY=${EMAIL_MARKETING_SOCKET_LABS_SERVER_SECONDARY} | tee -a .deployment-env
          - echo export SEMRUSH_API_KEY=${SEMRUSH_API_KEY} | tee -a .deployment-env
          - echo export GOOGLE_SOLAR_API_KEY=${GOOGLE_SOLAR_API_KEY} | tee -a .deployment-env
          - echo export SOLAR_CLASSIFIER_API_URL=${SOLAR_CLASSIFIER_API_URL} | tee -a .deployment-env
          - echo export SOLAR_CLASSIFIER_API_KEY=${SOLAR_CLASSIFIER_API_KEY} | tee -a .deployment-env
    - step: &run-tests
        name: Run Tests
        image: php:8.3-fpm
        services:
          - mysql
        size: 4x
        artifacts:
          - storage/logs/*
        script:
          - apt-get update && apt-get install --no-install-recommends -y unzip libcurl4 libcurl4-openssl-dev curl zip libzip-dev build-essential libpng-dev libjpeg62-turbo-dev jpegoptim optipng pngquant nginx libmagickwand-dev libxml2-dev mariadb-client git
          - docker-php-ext-install mysqli pdo pdo_mysql curl gd zip exif soap intl
          - curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
          - source .deployment-env
          - composer config -g github-oauth.github.com $GITHUB_API_TOKEN
          - composer install --ignore-platform-reqs
          - mysql --host=127.0.0.1 --user=root -e "CREATE DATABASE test_readonly;"
          - mysql --host=127.0.0.1 --user=root test_readonly < ./database/schema/readonly-schema.sql
          - touch .env
          - export APP_DEBUG="true"
          - export APP_ENV="testing"
          - export APP_KEY="base64:/NE/O9s7/GDnFuoHgxgh++qfChJ593PGyXwcdkVXNRE="
          - export APP_URL="https://localhost"
          - export BCRYPT_ROUNDS="4"
          - export BROADCAST_DRIVER="log"
          - export CACHE_DRIVER="array"
          - export DB_DATABASE="test_admin_20"
          - export DB_HOST="127.0.0.1"
          - export DB_PASSWORD=""
          - export DB_USERNAME="root"
          - export MAIL_MAILER="array"
          - export QUEUE_CONNECTION="sync"
          - export READONLY_DB_DATABASE="test_readonly"
          - export READONLY_DB_HOST="127.0.0.1"
          - export READONLY_DB_PASSWORD=""
          - export READONLY_DB_USERNAME="root"
          - export REDIS_CLIENT=""
          - export REPLICA_DB_HOST="127.0.0.1"
          - export REPLICA_TWO_DB_HOST="127.0.0.1"
          - export SESSION_DRIVER="array"
          - export TELESCOPE_ENABLED="false"
          - php -d memory_limit=4G artisan config:cache
          - php -d memory_limit=4G artisan config:clear
          - php -d memory_limit=4G artisan migrate
          - php -d memory_limit=4G artisan test
    - step: &build-frontend
        name: Build Front End
        image: google/cloud-sdk:latest
        services:
          - docker
        caches:
          - docker
        script:
          - echo "Starting Build"
          - source .deployment-env
          - export IMAGE_NAME="gcr.io/$PROJECT_ID/server:$DEPLOYMENT_NAME-$BITBUCKET_BUILD_NUMBER"
          - echo $KEY_FILE | base64 -d > ~/.gcloud-api-key.json
          - export USE_GKE_GCLOUD_AUTH_PLUGIN=True
          - gcloud auth activate-service-account --key-file ~/.gcloud-api-key.json
          - gcloud config set project $PROJECT_ID
          - gcloud auth configure-docker --quiet
          - echo "Building Docker"
          - docker build --build-arg google_ads_service_account=$GOOGLE_OAUTH_SERVICE_ACCOUNT --build-arg github_auth_api_key=$GITHUB_API_TOKEN -t $IMAGE_NAME .
          - docker push $IMAGE_NAME
    - step: &build-horizon-worker
        name: Build Horizon Worker
        image: google/cloud-sdk:latest
        services:
          - docker
        caches:
          - docker
        script:
          - echo "Starting Build"
          - source .deployment-env
          - export HORIZON_WORKER_IMAGE_NAME="gcr.io/$PROJECT_ID/horizon-worker:$DEPLOYMENT_NAME-$BITBUCKET_BUILD_NUMBER"
          - echo $KEY_FILE | base64 -d > ~/.gcloud-api-key.json
          - export USE_GKE_GCLOUD_AUTH_PLUGIN=True
          - gcloud auth activate-service-account --key-file ~/.gcloud-api-key.json
          - gcloud config set project $PROJECT_ID
          - gcloud auth configure-docker --quiet
          - echo "Building Docker"
          - docker build -f Dockerfile.horizon --build-arg google_ads_service_account=$GOOGLE_OAUTH_SERVICE_ACCOUNT --build-arg github_auth_api_key=$GITHUB_API_TOKEN -t $HORIZON_WORKER_IMAGE_NAME .
          - docker push $HORIZON_WORKER_IMAGE_NAME
    - step: &build-cron-server
        name: Build Cron Server
        image: google/cloud-sdk:latest
        services:
          - docker
        caches:
          - docker
        script:
          - echo "Starting Build"
          - source .deployment-env
          - export CRON_IMAGE_NAME="gcr.io/$PROJECT_ID/cron-server:$DEPLOYMENT_NAME-$BITBUCKET_BUILD_NUMBER"
          - echo $KEY_FILE | base64 -d > ~/.gcloud-api-key.json
          - export USE_GKE_GCLOUD_AUTH_PLUGIN=True
          - gcloud auth activate-service-account --key-file ~/.gcloud-api-key.json
          - gcloud config set project $PROJECT_ID
          - gcloud auth configure-docker --quiet
          - echo "Building Docker"
          - docker build -f Dockerfile.cron --build-arg google_ads_service_account=$GOOGLE_OAUTH_SERVICE_ACCOUNT --build-arg github_auth_api_key=$GITHUB_API_TOKEN -t $CRON_IMAGE_NAME .
          - docker push $CRON_IMAGE_NAME
    - step: &deploy
        name: Build Queue Worker
        image: google/cloud-sdk:latest
        services:
          - docker
        caches:
          - docker
        script:
          - echo "Starting Build"
          - source .deployment-env
          - export IMAGE_NAME="gcr.io/$PROJECT_ID/server:$DEPLOYMENT_NAME-$BITBUCKET_BUILD_NUMBER"
          - export QUEUE_IMAGE_NAME="gcr.io/$PROJECT_ID/queue-worker:$DEPLOYMENT_NAME-$BITBUCKET_BUILD_NUMBER"
          - export ALLOCATION_QUEUE_IMAGE_NAME="gcr.io/$PROJECT_ID/allocation-worker:$DEPLOYMENT_NAME-$BITBUCKET_BUILD_NUMBER"
          - export WORKFLOWS_QUEUE_IMAGE_NAME="gcr.io/$PROJECT_ID/workflows-worker:$DEPLOYMENT_NAME-$BITBUCKET_BUILD_NUMBER"
          - export MIGRATION_QUEUE_IMAGE_NAME="gcr.io/$PROJECT_ID/migration-worker:$DEPLOYMENT_NAME-$BITBUCKET_BUILD_NUMBER"
          - export APPOINTMENT_QUEUE_IMAGE_NAME="gcr.io/$PROJECT_ID/appointment-worker:$DEPLOYMENT_NAME-$BITBUCKET_BUILD_NUMBER"
          - export MAILBOX_QUEUE_IMAGE_NAME="gcr.io/$PROJECT_ID/mailbox-worker:$DEPLOYMENT_NAME-$BITBUCKET_BUILD_NUMBER"
          - export HORIZON_WORKER_IMAGE_NAME="gcr.io/$PROJECT_ID/horizon-worker:$DEPLOYMENT_NAME-$BITBUCKET_BUILD_NUMBER"
          - export CRON_IMAGE_NAME="gcr.io/$PROJECT_ID/cron-server:$DEPLOYMENT_NAME-$BITBUCKET_BUILD_NUMBER"
          - echo $KEY_FILE | base64 -d > ~/.gcloud-api-key.json
          - export USE_GKE_GCLOUD_AUTH_PLUGIN=True
          - export USE_GKE_GCLOUD_AUTH_PLUGIN=True
          - gcloud auth activate-service-account --key-file ~/.gcloud-api-key.json
          - gcloud config set project $PROJECT_ID
          - gcloud auth configure-docker --quiet
          - echo "Updating Cluster Deployment"
          - gcloud container clusters get-credentials $CLUSTER_NAME --zone=$DEFAULT_ZONE --project $PROJECT_ID
          - set +e
          - kubectl delete secret sr-admin-$DEPLOYMENT_NAME-secret
          - set -e
          - >
            kubectl create secret generic "sr-admin-$DEPLOYMENT_NAME-secret" \
              --from-literal=APP_NAME=$APP_NAME \
              --from-literal=APP_ENV=$APP_ENV \
              --from-literal=MIX_APP_ENV=$APP_ENV \
              --from-literal=APP_KEY=$APP_KEY \
              --from-literal=APP_DEBUG=$APP_DEBUG \
              --from-literal=APP_URL=$APP_URL \
              --from-literal=DB_CONNECTION=$DB_CONNECTION \
              --from-literal=DB_HOST=$DB_HOST \
              --from-literal=DB_PORT=$DB_PORT \
              --from-literal=DB_DATABASE=$DB_DATABASE \
              --from-literal=DB_USERNAME=$DB_USERNAME \
              --from-literal=DB_PASSWORD=$DB_PASSWORD \
              --from-literal=REPLICA_DB_CONNECTION=$REPLICA_DB_CONNECTION \
              --from-literal=REPLICA_DB_HOST=$REPLICA_DB_HOST \
              --from-literal=REPLICA_TWO_DB_HOST=$REPLICA_TWO_DB_HOST \
              --from-literal=REPLICA_DB_PORT=$REPLICA_DB_PORT \
              --from-literal=REPLICA_DB_DATABASE=$REPLICA_DB_DATABASE \
              --from-literal=REPLICA_DB_USERNAME=$REPLICA_DB_USERNAME \
              --from-literal=REPLICA_DB_PASSWORD=$REPLICA_DB_PASSWORD \
              --from-literal=READONLY_DB_CONNECTION=$DB_CONNECTION \
              --from-literal=READONLY_DB_HOST=$READONLY_DB_HOST \
              --from-literal=READONLY_DB_PORT=$READONLY_DB_PORT \
              --from-literal=READONLY_DB_DATABASE=$READONLY_DB_DATABASE \
              --from-literal=READONLY_DB_USERNAME=$READONLY_DB_USERNAME \
              --from-literal=READONLY_DB_PASSWORD=$READONLY_DB_PASSWORD \
              --from-literal=SENTRY_DSN=$SENTRY_DSN \
              --from-literal=MIX_SENTRY_DSN=$SENTRY_DSN \
              --from-literal=SENTRY_TRACE_QUEUE_ENABLED=$SENTRY_TRACE_QUEUE_ENABLED \
              --from-literal=SENTRY_SEND_DEFAULT_PII=$SENTRY_SEND_DEFAULT_PII \
              --from-literal=SENTRY_TRACES_SAMPLE_RATE=$SENTRY_TRACES_SAMPLE_RATE \
              --from-literal=LEAD_VERIFICATION_DRIVER=$LEAD_VERIFICATION_DRIVER \
              --from-literal=WHITEPAGES_IDENTITY_VERIFICATION_KEY=$WHITEPAGES_IDENTITY_VERIFICATION_KEY \
              --from-literal=WHITEPAGES_IDENTITY_VERIFICATION_URL=$WHITEPAGES_IDENTITY_VERIFICATION_URL \
              --from-literal=QUEUE_CONNECTION=$QUEUE_CONNECTION \
              --from-literal=REDIS_CLIENT=$REDIS_CLIENT \
              --from-literal=REDIS_HOST=$REDIS_HOST \
              --from-literal=REDIS_QUEUE_HOST=$REDIS_QUEUE_HOST \
              --from-literal=REDIS_QUEUE_PORT=$REDIS_QUEUE_PORT \
              --from-literal=SESSION_DRIVER=$SESSION_DRIVER \
              --from-literal=TWILIO_ML_SID=$TWILIO_ML_SID \
              --from-literal=TWILIO_SID=$TWILIO_SID \
              --from-literal=TWILIO_TOKEN=$TWILIO_TOKEN \
              --from-literal=TWILIO_REVIEW_REQUESTS=$TWILIO_REVIEW_REQUESTS \
              --from-literal=PUSHER_APP_ID=$PUSHER_APP_ID \
              --from-literal=PUSHER_APP_KEY=$PUSHER_APP_KEY \
              --from-literal=PUSHER_APP_SECRET=$PUSHER_APP_SECRET \
              --from-literal=PUSHER_APP_CLUSTER=$PUSHER_APP_CLUSTER \
              --from-literal=BROADCAST_DRIVER=$BROADCAST_DRIVER \
              --from-literal=CACHE_DRIVER=$CACHE_DRIVER \
              --from-literal=LEAD_COMMUNICATION_DRIVER=$COMMUNICATION_DRIVER \
              --from-literal=COMMUNICATION_DRIVER=$COMMUNICATION_DRIVER \
              --from-literal=ADMIN_INTEGRATION_CLIENT_SECRET=$ADMIN_INTEGRATION_CLIENT_SECRET \
              --from-literal=ADMIN_INTEGRATION_BASE_URL=$ADMIN_INTEGRATION_BASE_URL \
              --from-literal=LEAD_PROCESSING_API_DRIVER=$LEAD_PROCESSING_API_DRIVER \
              --from-literal=SALES_MANAGEMENT_API_DRIVER=$SALES_MANAGEMENT_API_DRIVER \
              --from-literal=USER_MANAGEMENT_API_DRIVER=$USER_MANAGEMENT_API_DRIVER \
              --from-literal=MAIL_MAILER=$MAIL_MAILER \
              --from-literal=MAIL_HOST=$MAIL_HOST \
              --from-literal=MAIL_PORT=$MAIL_PORT \
              --from-literal=MAIL_USERNAME=$MAIL_USERNAME \
              --from-literal=MAIL_PASSWORD=$MAIL_PASSWORD \
              --from-literal=MAIL_USERNAME_SECONDARY=$MAIL_USERNAME_SECONDARY \
              --from-literal=MAIL_PASSWORD_SECONDARY=$MAIL_PASSWORD_SECONDARY \
              --from-literal=MAIL_USERNAME_TERTIARY=$MAIL_USERNAME_TERTIARY \
              --from-literal=MAIL_PASSWORD_TERTIARY=$MAIL_PASSWORD_TERTIARY \
              --from-literal=MAIL_FROM_ADDRESS=$MAIL_FROM_ADDRESS \
              --from-literal=MAIL_FROM_NAME=$MAIL_FROM_NAME \
              --from-literal=SOLAR_MAIL_FROM_ADDRESS=$SOLAR_MAIL_FROM_ADDRESS \
              --from-literal=SOLAR_MAIL_FROM_NAME=$SOLAR_MAIL_FROM_NAME \
              --from-literal=ROOFING_MAIL_FROM_ADDRESS=$ROOFING_MAIL_FROM_ADDRESS \
              --from-literal=ROOFING_MAIL_FROM_NAME=$ROOFING_MAIL_FROM_NAME \
              --from-literal=LEGACY_ADMIN_AUTH_TOKEN=$LEGACY_ADMIN_AUTH_TOKEN \
              --from-literal=GOOGLE_STATIC_MAPS_API_KEY=$GOOGLE_STATIC_MAPS_API_KEY \
              --from-literal=GOOGLE_MAPS_GEOCODING_API_KEY=$GOOGLE_MAPS_GEOCODING_API_KEY \
              --from-literal=GOOGLE_STATIC_MAPS_API=$GOOGLE_STATIC_MAPS_API_KEY \
              --from-literal=GOOGLE_CLOUD_KEY_FILE=$GOOGLE_CLOUD_KEY_FILE \
              --from-literal=GOOGLE_STORAGE_INVOICES_BUCKET=$GOOGLE_STORAGE_INVOICES_BUCKET \
              --from-literal=PAYMENT_GATEWAY_DRIVER=$PAYMENT_GATEWAY_DRIVER \
              --from-literal=STRIPE_WEBHOOK_SIGNING_SECRET=$STRIPE_WEBHOOK_SIGNING_SECRET \
              --from-literal=GOOGLE_GMAIL_CLIENT_ID=$GOOGLE_GMAIL_CLIENT_ID \
              --from-literal=GOOGLE_GMAIL_CLIENT_SECRET=$GOOGLE_GMAIL_CLIENT_SECRET \
              --from-literal=GOOGLE_GMAIL_EMAIL_LISTENER_TOPIC=$GOOGLE_GMAIL_EMAIL_LISTENER_TOPIC \
              --from-literal=MAILBOX_MAIL_PROVIDER=$MAILBOX_MAIL_PROVIDER \
              --from-literal=MAILBOX_API_DRIVER=$MAILBOX_API_DRIVER \
              --from-literal=OUTGOING_COMMUNICATION_TEST_MODE=$OUTGOING_COMMUNICATION_TEST_MODE \
              --from-literal=MIX_LEGACY_ADMIN_BASE_URL=$MIX_LEGACY_ADMIN_BASE_URL \
              --from-literal=WWW_SOLARREVIEWS_DOMAIN=$WWW_SOLARREVIEWS_DOMAIN \
              --from-literal=GOOGLE_LOGGING_PROJECT_ID=$GOOGLE_LOGGING_PROJECT_ID \
              --from-literal=GOOGLE_PUBSUB_PROJECT_ID=$GOOGLE_PUBSUB_PROJECT_ID \
              --from-literal=GOOGLE_PUBSUB_TOPIC=$GOOGLE_PUBSUB_TOPIC \
              --from-literal=STACKDRIVER_ENABLED=$STACKDRIVER_ENABLED \
              --from-literal=GOOGLE_STORAGE_PROJECT_ID=$GOOGLE_STORAGE_PROJECT_ID \
              --from-literal=GOOGLE_ADS_DEVELOPER_TOKEN=$GOOGLE_ADS_DEVELOPER_TOKEN \
              --from-literal=GOOGLE_ADS_CLIENT_ID=$GOOGLE_ADS_CLIENT_ID \
              --from-literal=GOOGLE_ADS_CLIENT_SECRET=$GOOGLE_ADS_CLIENT_SECRET \
              --from-literal=GOOGLE_ADS_REFRESH_TOKEN=$GOOGLE_ADS_REFRESH_TOKEN \
              --from-literal=GOOGLE_ADS_IMPERSONATED_EMAIL=$GOOGLE_ADS_IMPERSONATED_EMAIL \
              --from-literal=GOOGLE_ADS_AUTH_DRIVER=$GOOGLE_ADS_AUTH_DRIVER \
              --from-literal=GOOGLE_ADS_GEOTARGETS_CSV_URL=$GOOGLE_ADS_GEOTARGETS_CSV_URL \
              --from-literal=ADVERTISING_DRIVER=$ADVERTISING_DRIVER \
              --from-literal=GOOGLE_STORAGE_EMAIL_TEMPLATE_IMAGES_BUCKET=$GOOGLE_STORAGE_EMAIL_TEMPLATE_IMAGES_BUCKET \
              --from-literal=CLIENT_JWT_SIGNING_KEY=$CLIENT_JWT_SIGNING_KEY \
              --from-literal=ADDRESS_IDENTIFICATION_DRIVER=$ADDRESS_IDENTIFICATION_DRIVER \
              --from-literal=GOOGLE_MAPS_PLACES_API_URI=$GOOGLE_MAPS_PLACES_API_URI \
              --from-literal=IP_QUALITY_SCORE_BASE_URL=$IP_QUALITY_SCORE_BASE_URL \
              --from-literal=IP_QUALITY_SCORE_API_KEY=$IP_QUALITY_SCORE_API_KEY \
              --from-literal=DATABASE_CONNECTION_RETRY_AFTER=$DATABASE_CONNECTION_RETRY_AFTER \
              --from-literal=CONSUMER_PRODUCT_VERIFICATION_DRIVER=$CONSUMER_PRODUCT_VERIFICATION_DRIVER \
              --from-literal=TINY_MCE_API_KEY=$TINY_MCE_API_KEY \
              --from-literal=TIME_MCE_STORAGE_DISK=$TIME_MCE_STORAGE_DISK \
              --from-literal=GOOGLE_STORAGE_TINYMCE_FILES_BUCKET=$GOOGLE_STORAGE_TINYMCE_FILES_BUCKET \
              --from-literal=GOOGLE_STORAGE_TINYMCE_FILE_URL=$GOOGLE_STORAGE_TINYMCE_FILE_URL \
              --from-literal=GOOGLE_STORAGE_MAILBOX_BUCKET=$GOOGLE_STORAGE_MAILBOX_BUCKET \
              --from-literal=GOOGLE_STORAGE_MAILBOX_CDN_URL=$GOOGLE_STORAGE_MAILBOX_CDN_URL \
              --from-literal=IP_QUALITY_SCORE_DRIVER=$IP_QUALITY_SCORE_DRIVER \
              --from-literal=MICROSOFT_ADS_CLIENT_ID=$MICROSOFT_ADS_CLIENT_ID \
              --from-literal=MICROSOFT_ADS_OAUTH_REDIRECT_URI=$MICROSOFT_ADS_OAUTH_REDIRECT_URI \
              --from-literal=MICROSOFT_ADS_CUSTOMER_ID=$MICROSOFT_ADS_CUSTOMER_ID \
              --from-literal=MICROSOFT_ADS_OFFLINE_CONVERSION_GOAL_NAME=$MICROSOFT_ADS_OFFLINE_CONVERSION_GOAL_NAME \
              --from-literal=MICROSOFT_ADS_DEVELOPER_TOKEN=$MICROSOFT_ADS_DEVELOPER_TOKEN \
              --from-literal=GOOGLE_ADS_CONVERSION_ACTION_NAME=$GOOGLE_ADS_CONVERSION_ACTION_NAME \
              --from-literal=MICROSOFT_ADS_CLIENT_SECRET=$MICROSOFT_ADS_CLIENT_SECRET \
              --from-literal=ADS_NOTIFICATION_EMAILS=$ADS_NOTIFICATION_EMAILS \
              --from-literal=PING_POST_NOTIFICATION_EMAILS=$PING_POST_NOTIFICATION_EMAILS \
              --from-literal=HUNTER_PRIORITY_CALCULATION_UNSOLD_DAYS=$HUNTER_PRIORITY_CALCULATION_UNSOLD_DAYS \
              --from-literal=FIXR_DOMAIN=$FIXR_DOMAIN \
              --from-literal=FIXR_ORIGIN_DOMAIN=$FIXR_ORIGIN_DOMAIN \
              --from-literal=AUTH_TOKEN_SINGING_KEY=$AUTH_TOKEN_SINGING_KEY \
              --from-literal=TWILIO_FROM_PHONE_NUMBER=$TWILIO_FROM_PHONE_NUMBER \
              --from-literal=TWILIO_CONSUMER_VERIFICATION_FROM_PHONE=$TWILIO_CONSUMER_VERIFICATION_FROM_PHONE \
              --from-literal=TWILIO_RECYCLED_LEADS_FROM_PHONE=$TWILIO_RECYCLED_LEADS_FROM_PHONE \
              --from-literal=META_ADS_APP_ID=$META_ADS_APP_ID \
              --from-literal=META_ADS_APP_SECRET=$META_ADS_APP_SECRET \
              --from-literal=META_ADS_BUSINESS_ID=$META_ADS_BUSINESS_ID \
              --from-literal=META_ADS_ADMIN_SYSTEM_USER_TOKEN=$META_ADS_ADMIN_SYSTEM_USER_TOKEN \
              --from-literal=COMPANY_DISCOVERY_GOOGLE_PLACES_API_KEY=$COMPANY_DISCOVERY_GOOGLE_PLACES_API_KEY \
              --from-literal=STRIPE_LIVE=$STRIPE_LIVE \
              --from-literal=STRIPE_API_VERSION=$STRIPE_API_VERSION \
              --from-literal=STRIPE_API_KEY_SECRET_TEST=$STRIPE_API_KEY_SECRET_TEST \
              --from-literal=STRIPE_API_KEY_SECRET_LIVE=$STRIPE_API_KEY_SECRET_LIVE \
              --from-literal=STRIPE_API_KEY_PUBLISHABLE_TEST=$STRIPE_API_KEY_PUBLISHABLE_TEST \
              --from-literal=STRIPE_API_KEY_PUBLISHABLE_LIVE=$STRIPE_API_KEY_PUBLISHABLE_LIVE \
              --from-literal=LOG_SLACK_WEBHOOK_URL=$LOG_SLACK_WEBHOOK_URL \
              --from-literal=DASHBOARD_CLIENT_API_JWT_EXPIRE_IN=$DASHBOARD_CLIENT_API_JWT_EXPIRE_IN \
              --from-literal=DASHBOARD_CLIENT_API_JWT_SIGNING_KEY=$DASHBOARD_CLIENT_API_JWT_SIGNING_KEY \
              --from-literal=LEAD_MINIMUM_ELECTRIC_SPEND=$LEAD_MINIMUM_ELECTRIC_SPEND \
              --from-literal=LEAD_REJECTION_PERCENTAGE_THRESHOLD=$LEAD_REJECTION_PERCENTAGE_THRESHOLD \
              --from-literal=LEAD_CRM_REJECTION_PERCENTAGE_THRESHOLD=$LEAD_CRM_REJECTION_PERCENTAGE_THRESHOLD \
              --from-literal=APPT_MINIMUM_ELECTRIC_SPEND=$APPT_MINIMUM_ELECTRIC_SPEND \
              --from-literal=APPT_REJECTION_PERCENTAGE_THRESHOLD=$APPT_REJECTION_PERCENTAGE_THRESHOLD \
              --from-literal=APPT_CRM_REJECTION_PERCENTAGE_THRESHOLD=$APPT_CRM_REJECTION_PERCENTAGE_THRESHOLD \
              --from-literal=MAX_CHARGE_ATTEMPTS=$MAX_CHARGE_ATTEMPTS \
              --from-literal=APPT_OFFERING_AVAILABLE_DURATION_MIN=$APPT_OFFERING_AVAILABLE_DURATION_MIN \
              --from-literal=APPTS_SOLARREVIEWS_DOMAIN=$APPTS_SOLARREVIEWS_DOMAIN \
              --from-literal=APPT_MAX_OFFERING_ATTEMPTS=$APPT_MAX_OFFERING_ATTEMPTS \
              --from-literal=APPT_REJECTION_WINDOW_DURATION_HOURS=$APPT_REJECTION_WINDOW_DURATION_HOURS \
              --from-literal=BRS_DRIVER=$BRS_DRIVER \
              --from-literal=APPT_CONSUMER_ADVANCE_NOTICE_HOURS=$APPT_CONSUMER_ADVANCE_NOTICE_HOURS \
              --from-literal=PRODUCT_PRICING_DRIVER=$PRODUCT_PRICING_DRIVER \
              --from-literal=LEAD_REJECTION_WINDOW_DURATION_HOURS=$LEAD_REJECTION_WINDOW_DURATION_HOURS \
              --from-literal=APPT_LOG_BRS_QUERIES=$APPT_LOG_BRS_QUERIES \
              --from-literal=GOOGLE_FIRESTORE_PROJECT_ID=$GOOGLE_FIRESTORE_PROJECT_ID \
              --from-literal=GOOGLE_FIRESTORE_KEY_FILE=$GOOGLE_FIRESTORE_KEY_FILE \
              --from-literal=GOOGLE_FIRESTORE_COLLECTION_PREFIX=$GOOGLE_FIRESTORE_COLLECTION_PREFIX \
              --from-literal=FLOW_BUILDER_IMAGE_BUCKET=$FLOW_BUILDER_IMAGE_BUCKET \
              --from-literal=FLOW_BUILDER_CDN_URL=$FLOW_BUILDER_CDN_URL \
              --from-literal=FLOW_BUILDER_URL=$FLOW_BUILDER_URL \
              --from-literal=FLOW_PROXY_URL=$FLOW_PROXY_URL \
              --from-literal=FLOW_PROXY_SECRET=$FLOW_PROXY_SECRET \
              --from-literal=FLOW_CLIENT_URL=$FLOW_CLIENT_URL \
              --from-literal=SCHEDULING_BASE_API_URL=$SCHEDULING_BASE_API_URL \
              --from-literal=SCHEDULING_CLIENT_SECRET=$SCHEDULING_CLIENT_SECRET \
              --from-literal=ALLOCATE_DIRECT_LEADS_WITHOUT_TIMEZONE_OPEN_DELAY=$ALLOCATE_DIRECT_LEADS_WITHOUT_TIMEZONE_OPEN_DELAY \
              --from-literal=LOG_JOB_EVENTS=$LOG_JOB_EVENTS \
              --from-literal=DATABASE_QUEUE_AFTER_COMMIT=$DATABASE_QUEUE_AFTER_COMMIT \
              --from-literal=APPT_NEXT_DELIVERY_ATTEMPT_BUFFER_MINUTES=$APPT_NEXT_DELIVERY_ATTEMPT_BUFFER_MINUTES \
              --from-literal=APPT_MAX_DELIVERY_ATTEMPTS=$APPT_MAX_DELIVERY_ATTEMPTS \
              --from-literal=GOOGLE_ADS_LOGIN_CUSTOMER_ID=$GOOGLE_ADS_LOGIN_CUSTOMER_ID \
              --from-literal=FILTERS_API_DRIVER=$FILTERS_API_DRIVER \
              --from-literal=FAILED_JOB_EMAILS=$FAILED_JOB_EMAILS \
              --from-literal=URL_SIGNER_SIGNATURE_KEY=$URL_SIGNER_SIGNATURE_KEY \
              --from-literal=OPPORTUNITY_NOTIFICATIONS_CRON=$OPPORTUNITY_NOTIFICATIONS_CRON \
              --from-literal=GOOGLE_DEBUG_MONITORING_LOGS=$GOOGLE_DEBUG_MONITORING_LOGS \
              --from-literal=APPT_SELL_APPT_AS_LEAD_BUFFER_AFTER_MIN=$APPT_SELL_APPT_AS_LEAD_BUFFER_AFTER_MIN \
              --from-literal=RUN_SCHEDULED_ADS_JOBS=$RUN_SCHEDULED_ADS_JOBS \
              --from-literal=MAILSLURP_API_KEY=$MAILSLURP_API_KEY \
              --from-literal=SOLAR_REVIEWS_FRONTEND_DOMAIN=$SOLAR_REVIEWS_FRONTEND_DOMAIN \
              --from-literal=FLOW_BUILDER_DEFAULT_SAFE_CALL_IDS=$FLOW_BUILDER_DEFAULT_SAFE_CALL_IDS \
              --from-literal=ARE_UTILITY_FILTERS_ACTIVE=$ARE_UTILITY_FILTERS_ACTIVE \
              --from-literal=COMPANY_SEARCH_FILTER_PURCHASING_FROM_COMPETITOR_QUERY_STRATEGY=$COMPANY_SEARCH_FILTER_PURCHASING_FROM_COMPETITOR_QUERY_STRATEGY \
              --from-literal=DELIVERY_FAILURE_NOTIFICATION_EMAILS=$DELIVERY_FAILURE_NOTIFICATION_EMAILS \
              --from-literal=DROPBOX_API_KEY=$DROPBOX_API_KEY \
              --from-literal=MIX_DROPBOX_ACCOUNT_ID=$MIX_DROPBOX_ACCOUNT_ID \
              --from-literal=COMPANY_METRICS_DRIVER=$COMPANY_METRICS_DRIVER \
              --from-literal=SIMILAR_WEB_API_BASE_URL=$SIMILAR_WEB_API_BASE_URL \
              --from-literal=SIMILAR_WEB_API_KEY=$SIMILAR_WEB_API_KEY \
              --from-literal=SPY_FU_API_BASE_URL=$SPY_FU_API_BASE_URL \
              --from-literal=SPY_FU_API_KEY=$SPY_FU_API_KEY \
              --from-literal=SPY_FU_API_ID=$SPY_FU_API_ID \
              --from-literal=GOOGLE_STORAGE_CONTRACT_BUCKET=$GOOGLE_STORAGE_CONTRACT_BUCKET \
              --from-literal=GOOGLE_STORAGE_CONTRACT_URL=$GOOGLE_STORAGE_CONTRACT_URL \
              --from-literal=LARGE_ACCOUNT_REVENUE_THRESHOLD=$LARGE_ACCOUNT_REVENUE_THRESHOLD \
              --from-literal=STAGING_TESTING_PUBSUB_TOPIC=$STAGING_TESTING_PUBSUB_TOPIC \
              --from-literal=FUTURE_ALLOCATION_TESTING_INDUSTRY_ID=$FUTURE_ALLOCATION_TESTING_INDUSTRY_ID \
              --from-literal=TWILIO_VERIFY_SERVICE_SID=$TWILIO_VERIFY_SERVICE_SID \
              --from-literal=META_ADS_AUTOMATION_SYSTEM_USER_NAME=$META_ADS_AUTOMATION_SYSTEM_USER_NAME \
              --from-literal=OMIT_LOW_NEVER_EXCEED_BUDGET=$OMIT_LOW_NEVER_EXCEED_BUDGET \
              --from-literal=META_WADE_ADS_APP_ID=$META_WADE_ADS_APP_ID \
              --from-literal=META_WADE_ADS_APP_SECRET=$META_WADE_ADS_APP_SECRET \
              --from-literal=META_WADE_ADS_BUSINESS_ID=$META_WADE_ADS_BUSINESS_ID \
              --from-literal=META_WADE_ADS_ADMIN_SYSTEM_USER_TOKEN=$META_WADE_ADS_ADMIN_SYSTEM_USER_TOKEN \
              --from-literal=META_WADE_ADS_PIXEL_ID=$META_WADE_ADS_PIXEL_ID \
              --from-literal=META_GABE_ADS_AD_COST_APP_ID=$META_GABE_ADS_AD_COST_APP_ID \
              --from-literal=META_GABE_ADS_AD_COST_APP_SECRET=$META_GABE_ADS_AD_COST_APP_SECRET \
              --from-literal=META_GABE_ADS_AD_COST_TOKEN=$META_GABE_ADS_AD_COST_TOKEN \
              --from-literal=META_WADE_ADS_AD_COST_APP_ID=$META_WADE_ADS_AD_COST_APP_ID \
              --from-literal=META_WADE_ADS_AD_COST_APP_SECRET=$META_WADE_ADS_AD_COST_APP_SECRET \
              --from-literal=META_WADE_ADS_AD_COST_TOKEN=$META_WADE_ADS_AD_COST_TOKEN \
              --from-literal=WATCHDOG_TWO_SERVER_URL=$WATCHDOG_TWO_SERVER_URL \
              --from-literal=WATCHDOG_TWO_PERSONAL_ACCESS_TOKEN=$WATCHDOG_TWO_PERSONAL_ACCESS_TOKEN \
              --from-literal=PRIVACY_ACCESS_TOKEN=$PRIVACY_ACCESS_TOKEN \
              --from-literal=SOLAR_ESTIMATE_DOMAIN=$SOLAR_ESTIMATE_DOMAIN \
              --from-literal=AFFILIATES_PORTAL_API_URL=$AFFILIATES_PORTAL_API_URL \
              --from-literal=AFFILIATES_PORTAL_API_TOKEN=$AFFILIATES_PORTAL_API_TOKEN \
              --from-literal=FLOW_ENGINE_URL=$FLOW_ENGINE_URL \
              --from-literal=EMAIL_MARKETING_API_KEY=$EMAIL_MARKETING_API_KEY \
              --from-literal=EMAIL_MARKETING_SERVER=$EMAIL_MARKETING_SERVER \
              --from-literal=EMAIL_MARKETING_DRIVER=$EMAIL_MARKETING_DRIVER \
              --from-literal=DIRECT_LEAD_ENABLED_INDUSTRY_IDS=$DIRECT_LEAD_ENABLED_INDUSTRY_IDS \
              --from-literal=CONTRACT_PROVIDER_INTEGRATION_KEY=$CONTRACT_PROVIDER_INTEGRATION_KEY \
              --from-literal=CONTRACT_PROVIDER_USER_ID=$CONTRACT_PROVIDER_USER_ID \
              --from-literal=CONTRACT_PROVIDER_ACCOUNT_ID=$CONTRACT_PROVIDER_ACCOUNT_ID \
              --from-literal=CONTRACT_PROVIDER_PRIVATE_KEY=$CONTRACT_PROVIDER_PRIVATE_KEY \
              --from-literal=EMAIL_MARKETING_SOCKET_LABS_SERVER=$EMAIL_MARKETING_SOCKET_LABS_SERVER \
              --from-literal=EMAIL_MARKETING_SOCKET_LABS_API_KEY=$EMAIL_MARKETING_SOCKET_LABS_API_KEY \
              --from-literal=SLACK_ADMIN_SYSTEM_BOT_AUTH_TOKEN=$SLACK_ADMIN_SYSTEM_BOT_AUTH_TOKEN \
              --from-literal=SLACK_COMPANY_REGISTRATION_BOT_AUTH_TOKEN=$SLACK_COMPANY_REGISTRATION_BOT_AUTH_TOKEN \
              --from-literal=SLACK_REGISTRATION_NOTIFICATION_CHANNEL_ID=$SLACK_REGISTRATION_NOTIFICATION_CHANNEL_ID \
              --from-literal=SALES_INTEL_API_KEY=$SALES_INTEL_API_KEY \
              --from-literal=GOOGLE_STORAGE_BASE_URL=$GOOGLE_STORAGE_BASE_URL \
              --from-literal=REVIEW_ATTACHMENTS_BUCKET=$REVIEW_ATTACHMENTS_BUCKET \
              --from-literal=EMAIL_MARKETING_SOCKET_LABS_WEBHOOK_SECRET=$EMAIL_MARKETING_SOCKET_LABS_WEBHOOK_SECRET \
              --from-literal=DEEPGRAM_API_KEY=$DEEPGRAM_API_KEY \
              --from-literal=EMAIL_MARKETING_SOCKET_LABS_API_KEY_SECONDARY=$EMAIL_MARKETING_SOCKET_LABS_API_KEY_SECONDARY \
              --from-literal=EMAIL_MARKETING_SOCKET_LABS_SERVER_SECONDARY=$EMAIL_MARKETING_SOCKET_LABS_SERVER_SECONDARY \
              --from-literal=SEMRUSH_API_KEY=$SEMRUSH_API_KEY \
              --from-literal=GOOGLE_SOLAR_API_KEY=$GOOGLE_SOLAR_API_KEY \
              --from-literal=SOLAR_CLASSIFIER_API_URL=$SOLAR_CLASSIFIER_API_URL \
              --from-literal=SOLAR_CLASSIFIER_API_KEY=$SOLAR_CLASSIFIER_API_KEY \

          - cp ./kubernetes/patch-secrets.yml ~/patch-secrets.yml
          - sed -i "s/sr-admin-example-secret/sr-admin-$DEPLOYMENT_NAME-secret/g" ~/patch-secrets.yml
          - kubectl patch deployment sr-admin-$DEPLOYMENT_NAME-deployment --patch "$(cat ~/patch-secrets.yml)"
          - kubectl patch deployment sr-admin-$DEPLOYMENT_NAME-webhook-deployment --patch "$(cat ~/patch-secrets.yml)"
          - kubectl patch deployment sr-admin-$DEPLOYMENT_NAME-horizon-deployment --patch "$(cat ~/patch-secrets.yml)"
          - kubectl patch deployment sr-admin-$DEPLOYMENT_NAME-cron-server-deployment --patch "$(cat ~/patch-secrets.yml)"
          - kubectl set image deployment/sr-admin-$DEPLOYMENT_NAME-deployment server=$IMAGE_NAME
          - kubectl set image deployment/sr-admin-$DEPLOYMENT_NAME-webhook-deployment server=$IMAGE_NAME
          - kubectl set image deployment/sr-admin-$DEPLOYMENT_NAME-horizon-deployment server=$HORIZON_WORKER_IMAGE_NAME
          - kubectl set image deployment/sr-admin-$DEPLOYMENT_NAME-cron-server-deployment server=$CRON_IMAGE_NAME
          - curl -sL https://sentry.io/get-cli/ | SENTRY_CLI_VERSION="2.0.4" bash
          - VERSION_NUMBER=$(sentry-cli --auth-token $SENTRY_AUTH_TOKEN releases propose-version)
          - BUILD_DATE=$(date +"%Y.%m.%d")
          - VERSION="$BUILD_DATE-$VERSION_NUMBER"
          - sentry-cli --auth-token $SENTRY_AUTH_TOKEN releases -o solar-investments new -p admin-20 "$VERSION"
          - sentry-cli --auth-token $SENTRY_AUTH_TOKEN releases set-commits --ignore-missing --auto -p admin-20 -o solar-investments "$VERSION"
          - sentry-cli --auth-token $SENTRY_AUTH_TOKEN releases -o solar-investments deploys "$VERSION" new -e $DEPLOYMENT_NAME
          - sentry-cli --auth-token $SENTRY_AUTH_TOKEN releases -o solar-investments finalize "$VERSION"
    - step: &build-and-push
        name: Build and Push
        image: google/cloud-sdk:latest
        services:
          - docker
        caches:
          - docker
        script:
          - echo "Starting Build"
          - source .deployment-env
          - export IMAGE_NAME="gcr.io/$PROJECT_ID/server:$DEPLOYMENT_NAME-$BITBUCKET_BUILD_NUMBER"
          - export QUEUE_IMAGE_NAME="gcr.io/$PROJECT_ID/queue-worker:$DEPLOYMENT_NAME-$BITBUCKET_BUILD_NUMBER"
          - export ALLOCATION_QUEUE_IMAGE_NAME="gcr.io/$PROJECT_ID/allocation-worker:$DEPLOYMENT_NAME-$BITBUCKET_BUILD_NUMBER"
          - export WORKFLOWS_QUEUE_IMAGE_NAME="gcr.io/$PROJECT_ID/workflows-worker:$DEPLOYMENT_NAME-$BITBUCKET_BUILD_NUMBER"
          - export MIGRATION_QUEUE_IMAGE_NAME="gcr.io/$PROJECT_ID/migration-worker:$DEPLOYMENT_NAME-$BITBUCKET_BUILD_NUMBER"
          - export APPOINTMENT_QUEUE_IMAGE_NAME="gcr.io/$PROJECT_ID/appointment-worker:$DEPLOYMENT_NAME-$BITBUCKET_BUILD_NUMBER"
          - export MAILBOX_QUEUE_IMAGE_NAME="gcr.io/$PROJECT_ID/mailbox-worker:$DEPLOYMENT_NAME-$BITBUCKET_BUILD_NUMBER"
          - export HORIZON_WORKER_IMAGE_NAME="gcr.io/$PROJECT_ID/horizon-worker:$DEPLOYMENT_NAME-$BITBUCKET_BUILD_NUMBER"
          - export CRON_IMAGE_NAME="gcr.io/$PROJECT_ID/cron-server:$DEPLOYMENT_NAME-$BITBUCKET_BUILD_NUMBER"
          - echo $KEY_FILE | base64 -d > ~/.gcloud-api-key.json
          - export USE_GKE_GCLOUD_AUTH_PLUGIN=True
          - gcloud auth activate-service-account --key-file ~/.gcloud-api-key.json
          - gcloud config set project $PROJECT_ID
          - gcloud auth configure-docker --quiet
          - echo "Building Docker"
          - docker build --build-arg google_ads_service_account=$GOOGLE_OAUTH_SERVICE_ACCOUNT --build-arg github_auth_api_key=$GITHUB_API_TOKEN -t $IMAGE_NAME .
          - docker push $IMAGE_NAME
          - docker build -f Dockerfile.horizon --build-arg google_ads_service_account=$GOOGLE_OAUTH_SERVICE_ACCOUNT --build-arg github_auth_api_key=$GITHUB_API_TOKEN -t $HORIZON_WORKER_IMAGE_NAME .
          - docker push $HORIZON_WORKER_IMAGE_NAME
          - docker build -f Dockerfile.cron --build-arg google_ads_service_account=$GOOGLE_OAUTH_SERVICE_ACCOUNT --build-arg github_auth_api_key=$GITHUB_API_TOKEN -t $CRON_IMAGE_NAME .
          - docker push $CRON_IMAGE_NAME
          - echo "Updating Cluster Deployment"
          - gcloud container clusters get-credentials $CLUSTER_NAME --zone=$DEFAULT_ZONE --project $PROJECT_ID
          - set +e
          - kubectl delete secret sr-admin-$DEPLOYMENT_NAME-secret
          - set -e
          - >
            kubectl create secret generic "sr-admin-$DEPLOYMENT_NAME-secret" \
              --from-literal=APP_NAME=$APP_NAME \
              --from-literal=APP_ENV=$APP_ENV \
              --from-literal=MIX_APP_ENV=$APP_ENV \
              --from-literal=APP_KEY=$APP_KEY \
              --from-literal=APP_DEBUG=$APP_DEBUG \
              --from-literal=APP_URL=$APP_URL \
              --from-literal=DB_CONNECTION=$DB_CONNECTION \
              --from-literal=DB_HOST=$DB_HOST \
              --from-literal=DB_PORT=$DB_PORT \
              --from-literal=DB_DATABASE=$DB_DATABASE \
              --from-literal=DB_USERNAME=$DB_USERNAME \
              --from-literal=DB_PASSWORD=$DB_PASSWORD \
              --from-literal=READONLY_DB_CONNECTION=$DB_CONNECTION \
              --from-literal=READONLY_DB_HOST=$READONLY_DB_HOST \
              --from-literal=READONLY_DB_PORT=$READONLY_DB_PORT \
              --from-literal=READONLY_DB_DATABASE=$READONLY_DB_DATABASE \
              --from-literal=READONLY_DB_USERNAME=$READONLY_DB_USERNAME \
              --from-literal=READONLY_DB_PASSWORD=$READONLY_DB_PASSWORD \
              --from-literal=SENTRY_DSN=$SENTRY_DSN \
              --from-literal=MIX_SENTRY_DSN=$SENTRY_DSN \
              --from-literal=SENTRY_TRACE_QUEUE_ENABLED=$SENTRY_TRACE_QUEUE_ENABLED \
              --from-literal=SENTRY_SEND_DEFAULT_PII=$SENTRY_SEND_DEFAULT_PII \
              --from-literal=SENTRY_TRACES_SAMPLE_RATE=$SENTRY_TRACES_SAMPLE_RATE \
              --from-literal=LEAD_VERIFICATION_DRIVER=$LEAD_VERIFICATION_DRIVER \
              --from-literal=WHITEPAGES_IDENTITY_VERIFICATION_KEY=$WHITEPAGES_IDENTITY_VERIFICATION_KEY \
              --from-literal=WHITEPAGES_IDENTITY_VERIFICATION_URL=$WHITEPAGES_IDENTITY_VERIFICATION_URL \
              --from-literal=QUEUE_CONNECTION=$QUEUE_CONNECTION \
              --from-literal=REDIS_CLIENT=$REDIS_CLIENT \
              --from-literal=REDIS_HOST=$REDIS_HOST \
              --from-literal=REDIS_QUEUE_HOST=$REDIS_QUEUE_HOST \
              --from-literal=REDIS_QUEUE_PORT=$REDIS_QUEUE_PORT \
              --from-literal=SESSION_DRIVER=$SESSION_DRIVER \
              --from-literal=TWILIO_ML_SID=$TWILIO_ML_SID \
              --from-literal=TWILIO_SID=$TWILIO_SID \
              --from-literal=TWILIO_TOKEN=$TWILIO_TOKEN \
              --from-literal=TWILIO_REVIEW_REQUESTS=$TWILIO_REVIEW_REQUESTS \
              --from-literal=PUSHER_APP_ID=$PUSHER_APP_ID \
              --from-literal=PUSHER_APP_KEY=$PUSHER_APP_KEY \
              --from-literal=PUSHER_APP_SECRET=$PUSHER_APP_SECRET \
              --from-literal=PUSHER_APP_CLUSTER=$PUSHER_APP_CLUSTER \
              --from-literal=BROADCAST_DRIVER=$BROADCAST_DRIVER \
              --from-literal=CACHE_DRIVER=$CACHE_DRIVER \
              --from-literal=LEAD_COMMUNICATION_DRIVER=$COMMUNICATION_DRIVER \
              --from-literal=COMMUNICATION_DRIVER=$COMMUNICATION_DRIVER \
              --from-literal=ADMIN_INTEGRATION_CLIENT_SECRET=$ADMIN_INTEGRATION_CLIENT_SECRET \
              --from-literal=ADMIN_INTEGRATION_BASE_URL=$ADMIN_INTEGRATION_BASE_URL \
              --from-literal=LEAD_PROCESSING_API_DRIVER=$LEAD_PROCESSING_API_DRIVER \
              --from-literal=SALES_MANAGEMENT_API_DRIVER=$SALES_MANAGEMENT_API_DRIVER \
              --from-literal=USER_MANAGEMENT_API_DRIVER=$USER_MANAGEMENT_API_DRIVER \
              --from-literal=MAIL_MAILER=$MAIL_MAILER \
              --from-literal=MAIL_HOST=$MAIL_HOST \
              --from-literal=MAIL_PORT=$MAIL_PORT \
              --from-literal=MAIL_USERNAME=$MAIL_USERNAME \
              --from-literal=MAIL_PASSWORD=$MAIL_PASSWORD \
              --from-literal=MAIL_USERNAME_SECONDARY=$MAIL_USERNAME_SECONDARY \
              --from-literal=MAIL_PASSWORD_SECONDARY=$MAIL_PASSWORD_SECONDARY \
              --from-literal=MAIL_USERNAME_TERTIARY=$MAIL_USERNAME_TERTIARY \
              --from-literal=MAIL_PASSWORD_TERTIARY=$MAIL_PASSWORD_TERTIARY \
              --from-literal=MAIL_FROM_ADDRESS=$MAIL_FROM_ADDRESS \
              --from-literal=MAIL_FROM_NAME=$MAIL_FROM_NAME \
              --from-literal=SOLAR_MAIL_FROM_ADDRESS=$SOLAR_MAIL_FROM_ADDRESS \
              --from-literal=SOLAR_MAIL_FROM_NAME=$SOLAR_MAIL_FROM_NAME \
              --from-literal=ROOFING_MAIL_FROM_ADDRESS=$ROOFING_MAIL_FROM_ADDRESS \
              --from-literal=ROOFING_MAIL_FROM_NAME=$ROOFING_MAIL_FROM_NAME \
              --from-literal=LEGACY_ADMIN_AUTH_TOKEN=$LEGACY_ADMIN_AUTH_TOKEN \
              --from-literal=GOOGLE_STATIC_MAPS_API_KEY=$GOOGLE_STATIC_MAPS_API_KEY \
              --from-literal=GOOGLE_MAPS_GEOCODING_API_KEY=$GOOGLE_MAPS_GEOCODING_API_KEY \
              --from-literal=GOOGLE_STATIC_MAPS_API=$GOOGLE_STATIC_MAPS_API_KEY \
              --from-literal=GOOGLE_CLOUD_KEY_FILE=$GOOGLE_CLOUD_KEY_FILE \
              --from-literal=GOOGLE_STORAGE_INVOICES_BUCKET=$GOOGLE_STORAGE_INVOICES_BUCKET \
              --from-literal=PAYMENT_GATEWAY_DRIVER=$PAYMENT_GATEWAY_DRIVER \
              --from-literal=STRIPE_WEBHOOK_SIGNING_SECRET=$STRIPE_WEBHOOK_SIGNING_SECRET \
              --from-literal=GOOGLE_GMAIL_CLIENT_ID=$GOOGLE_GMAIL_CLIENT_ID \
              --from-literal=GOOGLE_GMAIL_CLIENT_SECRET=$GOOGLE_GMAIL_CLIENT_SECRET \
              --from-literal=GOOGLE_GMAIL_EMAIL_LISTENER_TOPIC=$GOOGLE_GMAIL_EMAIL_LISTENER_TOPIC \
              --from-literal=MAILBOX_MAIL_PROVIDER=$MAILBOX_MAIL_PROVIDER \
              --from-literal=MAILBOX_API_DRIVER=$MAILBOX_API_DRIVER \
              --from-literal=OUTGOING_COMMUNICATION_TEST_MODE=$OUTGOING_COMMUNICATION_TEST_MODE \
              --from-literal=MIX_LEGACY_ADMIN_BASE_URL=$MIX_LEGACY_ADMIN_BASE_URL \
              --from-literal=WWW_SOLARREVIEWS_DOMAIN=$WWW_SOLARREVIEWS_DOMAIN \
              --from-literal=GOOGLE_LOGGING_PROJECT_ID=$GOOGLE_LOGGING_PROJECT_ID \
              --from-literal=GOOGLE_PUBSUB_PROJECT_ID=$GOOGLE_PUBSUB_PROJECT_ID \
              --from-literal=GOOGLE_PUBSUB_TOPIC=$GOOGLE_PUBSUB_TOPIC \
              --from-literal=STACKDRIVER_ENABLED=$STACKDRIVER_ENABLED \
              --from-literal=GOOGLE_STORAGE_PROJECT_ID=$GOOGLE_STORAGE_PROJECT_ID \
              --from-literal=ADVERTISING_DRIVER=$ADVERTISING_DRIVER \
              --from-literal=GOOGLE_ADS_DEVELOPER_TOKEN=$GOOGLE_ADS_DEVELOPER_TOKEN \
              --from-literal=GOOGLE_ADS_CLIENT_ID=$GOOGLE_ADS_CLIENT_ID \
              --from-literal=GOOGLE_ADS_CLIENT_SECRET=$GOOGLE_ADS_CLIENT_SECRET \
              --from-literal=GOOGLE_ADS_REFRESH_TOKEN=$GOOGLE_ADS_REFRESH_TOKEN \
              --from-literal=GOOGLE_ADS_GEOTARGETS_CSV_URL=$GOOGLE_ADS_GEOTARGETS_CSV_URL \
              --from-literal=GOOGLE_ADS_IMPERSONATED_EMAIL=$GOOGLE_ADS_IMPERSONATED_EMAIL \
              --from-literal=GOOGLE_ADS_AUTH_DRIVER=$GOOGLE_ADS_AUTH_DRIVER \
              --from-literal=GOOGLE_STORAGE_EMAIL_TEMPLATE_IMAGES_BUCKET=$GOOGLE_STORAGE_EMAIL_TEMPLATE_IMAGES_BUCKET \
              --from-literal=CLIENT_JWT_SIGNING_KEY=$CLIENT_JWT_SIGNING_KEY \
              --from-literal=ADDRESS_IDENTIFICATION_DRIVER=$ADDRESS_IDENTIFICATION_DRIVER \
              --from-literal=GOOGLE_MAPS_PLACES_API_URI=$GOOGLE_MAPS_PLACES_API_URI \
              --from-literal=IP_QUALITY_SCORE_BASE_URL=$IP_QUALITY_SCORE_BASE_URL \
              --from-literal=IP_QUALITY_SCORE_API_KEY=$IP_QUALITY_SCORE_API_KEY \
              --from-literal=DATABASE_CONNECTION_RETRY_AFTER=$DATABASE_CONNECTION_RETRY_AFTER \
              --from-literal=CONSUMER_PRODUCT_VERIFICATION_DRIVER=$CONSUMER_PRODUCT_VERIFICATION_DRIVER \
              --from-literal=TINY_MCE_API_KEY=$TINY_MCE_API_KEY \
              --from-literal=TIME_MCE_STORAGE_DISK=$TIME_MCE_STORAGE_DISK \
              --from-literal=GOOGLE_STORAGE_TINYMCE_FILES_BUCKET=$GOOGLE_STORAGE_TINYMCE_FILES_BUCKET \
              --from-literal=GOOGLE_STORAGE_TINYMCE_FILE_URL=$GOOGLE_STORAGE_TINYMCE_FILE_URL \
              --from-literal=GOOGLE_STORAGE_MAILBOX_BUCKET=$GOOGLE_STORAGE_MAILBOX_BUCKET \
              --from-literal=GOOGLE_STORAGE_MAILBOX_CDN_URL=$GOOGLE_STORAGE_MAILBOX_CDN_URL \
              --from-literal=IP_QUALITY_SCORE_DRIVER=$IP_QUALITY_SCORE_DRIVER \
              --from-literal=MICROSOFT_ADS_CLIENT_ID=$MICROSOFT_ADS_CLIENT_ID \
              --from-literal=MICROSOFT_ADS_OAUTH_REDIRECT_URI=$MICROSOFT_ADS_OAUTH_REDIRECT_URI \
              --from-literal=MICROSOFT_ADS_CUSTOMER_ID=$MICROSOFT_ADS_CUSTOMER_ID \
              --from-literal=MICROSOFT_ADS_OFFLINE_CONVERSION_GOAL_NAME=$MICROSOFT_ADS_OFFLINE_CONVERSION_GOAL_NAME \
              --from-literal=MICROSOFT_ADS_DEVELOPER_TOKEN=$MICROSOFT_ADS_DEVELOPER_TOKEN \
              --from-literal=GOOGLE_ADS_CONVERSION_ACTION_NAME=$GOOGLE_ADS_CONVERSION_ACTION_NAME \
              --from-literal=MICROSOFT_ADS_CLIENT_SECRET=$MICROSOFT_ADS_CLIENT_SECRET \
              --from-literal=ADS_NOTIFICATION_EMAILS=$ADS_NOTIFICATION_EMAILS \
              --from-literal=PING_POST_NOTIFICATION_EMAILS=$PING_POST_NOTIFICATION_EMAILS \
              --from-literal=FIXR_DOMAIN=$FIXR_DOMAIN \
              --from-literal=FIXR_ORIGIN_DOMAIN=$FIXR_ORIGIN_DOMAIN \
              --from-literal=AUTH_TOKEN_SINGING_KEY=$AUTH_TOKEN_SINGING_KEY \
              --from-literal=TWILIO_FROM_PHONE_NUMBER=$TWILIO_FROM_PHONE_NUMBER \
              --from-literal=TWILIO_CONSUMER_VERIFICATION_FROM_PHONE=$TWILIO_CONSUMER_VERIFICATION_FROM_PHONE \
              --from-literal=TWILIO_RECYCLED_LEADS_FROM_PHONE=$TWILIO_RECYCLED_LEADS_FROM_PHONE \
              --from-literal=META_ADS_APP_ID=$META_ADS_APP_ID \
              --from-literal=META_ADS_APP_SECRET=$META_ADS_APP_SECRET \
              --from-literal=META_ADS_BUSINESS_ID=$META_ADS_BUSINESS_ID \
              --from-literal=META_ADS_ADMIN_SYSTEM_USER_TOKEN=$META_ADS_ADMIN_SYSTEM_USER_TOKEN \
              --from-literal=COMPANY_DISCOVERY_GOOGLE_PLACES_API_KEY=$COMPANY_DISCOVERY_GOOGLE_PLACES_API_KEY \
              --from-literal=STRIPE_LIVE=$STRIPE_LIVE \
              --from-literal=STRIPE_API_VERSION=$STRIPE_API_VERSION \
              --from-literal=STRIPE_API_KEY_SECRET_TEST=$STRIPE_API_KEY_SECRET_TEST \
              --from-literal=STRIPE_API_KEY_SECRET_LIVE=$STRIPE_API_KEY_SECRET_LIVE \
              --from-literal=STRIPE_API_KEY_PUBLISHABLE_TEST=$STRIPE_API_KEY_PUBLISHABLE_TEST \
              --from-literal=STRIPE_API_KEY_PUBLISHABLE_LIVE=$STRIPE_API_KEY_PUBLISHABLE_LIVE \
              --from-literal=LOG_SLACK_WEBHOOK_URL=$LOG_SLACK_WEBHOOK_URL \
              --from-literal=HUNTER_PRIORITY_CALCULATION_UNSOLD_DAYS=$HUNTER_PRIORITY_CALCULATION_UNSOLD_DAYS \
              --from-literal=DASHBOARD_CLIENT_API_JWT_EXPIRE_IN=$DASHBOARD_CLIENT_API_JWT_EXPIRE_IN \
              --from-literal=DASHBOARD_CLIENT_API_JWT_SIGNING_KEY=$DASHBOARD_CLIENT_API_JWT_SIGNING_KEY \
              --from-literal=LEAD_MINIMUM_ELECTRIC_SPEND=$LEAD_MINIMUM_ELECTRIC_SPEND \
              --from-literal=LEAD_REJECTION_PERCENTAGE_THRESHOLD=$LEAD_REJECTION_PERCENTAGE_THRESHOLD \
              --from-literal=LEAD_CRM_REJECTION_PERCENTAGE_THRESHOLD=$LEAD_CRM_REJECTION_PERCENTAGE_THRESHOLD \
              --from-literal=APPT_MINIMUM_ELECTRIC_SPEND=$APPT_MINIMUM_ELECTRIC_SPEND \
              --from-literal=APPT_REJECTION_PERCENTAGE_THRESHOLD=$APPT_REJECTION_PERCENTAGE_THRESHOLD \
              --from-literal=APPT_CRM_REJECTION_PERCENTAGE_THRESHOLD=$APPT_CRM_REJECTION_PERCENTAGE_THRESHOLD \
              --from-literal=MAX_CHARGE_ATTEMPTS=$MAX_CHARGE_ATTEMPTS \
              --from-literal=APPT_OFFERING_AVAILABLE_DURATION_MIN=$APPT_OFFERING_AVAILABLE_DURATION_MIN \
              --from-literal=APPTS_SOLARREVIEWS_DOMAIN=$APPTS_SOLARREVIEWS_DOMAIN \
              --from-literal=APPT_MAX_OFFERING_ATTEMPTS=$APPT_MAX_OFFERING_ATTEMPTS \
              --from-literal=APPT_REJECTION_WINDOW_DURATION_HOURS=$APPT_REJECTION_WINDOW_DURATION_HOURS \
              --from-literal=BRS_DRIVER=$BRS_DRIVER \
              --from-literal=APPT_CONSUMER_ADVANCE_NOTICE_HOURS=$APPT_CONSUMER_ADVANCE_NOTICE_HOURS \
              --from-literal=PRODUCT_PRICING_DRIVER=$PRODUCT_PRICING_DRIVER \
              --from-literal=LEAD_REJECTION_WINDOW_DURATION_HOURS=$LEAD_REJECTION_WINDOW_DURATION_HOURS \
              --from-literal=APPT_LOG_BRS_QUERIES=$APPT_LOG_BRS_QUERIES \
              --from-literal=GOOGLE_FIRESTORE_PROJECT_ID=$GOOGLE_FIRESTORE_PROJECT_ID \
              --from-literal=GOOGLE_FIRESTORE_KEY_FILE=$GOOGLE_FIRESTORE_KEY_FILE \
              --from-literal=GOOGLE_FIRESTORE_COLLECTION_PREFIX=$GOOGLE_FIRESTORE_COLLECTION_PREFIX \
              --from-literal=FLOW_BUILDER_IMAGE_BUCKET=$FLOW_BUILDER_IMAGE_BUCKET \
              --from-literal=FLOW_BUILDER_CDN_URL=$FLOW_BUILDER_CDN_URL \
              --from-literal=FLOW_BUILDER_URL=$FLOW_BUILDER_URL \
              --from-literal=FLOW_PROXY_URL=$FLOW_PROXY_URL \
              --from-literal=FLOW_PROXY_SECRET=$FLOW_PROXY_SECRET \
              --from-literal=FLOW_CLIENT_URL=$FLOW_CLIENT_URL \
              --from-literal=LOG_JOB_EVENTS=$LOG_JOB_EVENTS \
              --from-literal=SCHEDULING_BASE_API_URL=$SCHEDULING_BASE_API_URL \
              --from-literal=SCHEDULING_CLIENT_SECRET=$SCHEDULING_CLIENT_SECRET \
              --from-literal=ALLOCATE_DIRECT_LEADS_WITHOUT_TIMEZONE_OPEN_DELAY=$ALLOCATE_DIRECT_LEADS_WITHOUT_TIMEZONE_OPEN_DELAY \
              --from-literal=DATABASE_QUEUE_AFTER_COMMIT=$DATABASE_QUEUE_AFTER_COMMIT \
              --from-literal=APPT_NEXT_DELIVERY_ATTEMPT_BUFFER_MINUTES=$APPT_NEXT_DELIVERY_ATTEMPT_BUFFER_MINUTES \
              --from-literal=APPT_MAX_DELIVERY_ATTEMPTS=$APPT_MAX_DELIVERY_ATTEMPTS \
              --from-literal=GOOGLE_ADS_LOGIN_CUSTOMER_ID=$GOOGLE_ADS_LOGIN_CUSTOMER_ID \
              --from-literal=URL_SIGNER_SIGNATURE_KEY=$URL_SIGNER_SIGNATURE_KEY \
              --from-literal=OPPORTUNITY_NOTIFICATIONS_CRON=$OPPORTUNITY_NOTIFICATIONS_CRON \
              --from-literal=GOOGLE_DEBUG_MONITORING_LOGS=$GOOGLE_DEBUG_MONITORING_LOGS \
              --from-literal=APPT_SELL_APPT_AS_LEAD_BUFFER_AFTER_MIN=$APPT_SELL_APPT_AS_LEAD_BUFFER_AFTER_MIN \
              --from-literal=FILTERS_API_DRIVER=$FILTERS_API_DRIVER \
              --from-literal=FAILED_JOB_EMAILS=$FAILED_JOB_EMAILS \
              --from-literal=RUN_SCHEDULED_ADS_JOBS=$RUN_SCHEDULED_ADS_JOBS \
              --from-literal=MAILSLURP_API_KEY=$MAILSLURP_API_KEY \
              --from-literal=SOLAR_REVIEWS_FRONTEND_DOMAIN=$SOLAR_REVIEWS_FRONTEND_DOMAIN \
              --from-literal=FLOW_BUILDER_DEFAULT_SAFE_CALL_IDS=$FLOW_BUILDER_DEFAULT_SAFE_CALL_IDS \
              --from-literal=ARE_UTILITY_FILTERS_ACTIVE=$ARE_UTILITY_FILTERS_ACTIVE \
              --from-literal=COMPANY_SEARCH_FILTER_PURCHASING_FROM_COMPETITOR_QUERY_STRATEGY=$COMPANY_SEARCH_FILTER_PURCHASING_FROM_COMPETITOR_QUERY_STRATEGY \
              --from-literal=DELIVERY_FAILURE_NOTIFICATION_EMAILS=$DELIVERY_FAILURE_NOTIFICATION_EMAILS \
              --from-literal=DROPBOX_API_KEY=$DROPBOX_API_KEY \
              --from-literal=MIX_DROPBOX_ACCOUNT_ID=$MIX_DROPBOX_ACCOUNT_ID \
              --from-literal=COMPANY_METRICS_DRIVER=$COMPANY_METRICS_DRIVER \
              --from-literal=SIMILAR_WEB_API_BASE_URL=$SIMILAR_WEB_API_BASE_URL \
              --from-literal=SIMILAR_WEB_API_KEY=$SIMILAR_WEB_API_KEY \
              --from-literal=SPY_FU_API_BASE_URL=$SPY_FU_API_BASE_URL \
              --from-literal=SPY_FU_API_KEY=$SPY_FU_API_KEY \
              --from-literal=SPY_FU_API_ID=$SPY_FU_API_ID \
              --from-literal=GOOGLE_STORAGE_CONTRACT_BUCKET=$GOOGLE_STORAGE_CONTRACT_BUCKET \
              --from-literal=GOOGLE_STORAGE_CONTRACT_URL=$GOOGLE_STORAGE_CONTRACT_URL \
              --from-literal=LARGE_ACCOUNT_REVENUE_THRESHOLD=$LARGE_ACCOUNT_REVENUE_THRESHOLD \
              --from-literal=STAGING_TESTING_PUBSUB_TOPIC=$STAGING_TESTING_PUBSUB_TOPIC \
              --from-literal=FUTURE_ALLOCATION_TESTING_INDUSTRY_ID=$FUTURE_ALLOCATION_TESTING_INDUSTRY_ID \
              --from-literal=TWILIO_VERIFY_SERVICE_SID=$TWILIO_VERIFY_SERVICE_SID \
              --from-literal=META_ADS_AUTOMATION_SYSTEM_USER_NAME=$META_ADS_AUTOMATION_SYSTEM_USER_NAME \
              --from-literal=OMIT_LOW_NEVER_EXCEED_BUDGET=$OMIT_LOW_NEVER_EXCEED_BUDGET \
              --from-literal=META_WADE_ADS_APP_ID=$META_WADE_ADS_APP_ID \
              --from-literal=META_WADE_ADS_APP_SECRET=$META_WADE_ADS_APP_SECRET \
              --from-literal=META_WADE_ADS_BUSINESS_ID=$META_WADE_ADS_BUSINESS_ID \
              --from-literal=META_WADE_ADS_ADMIN_SYSTEM_USER_TOKEN=$META_WADE_ADS_ADMIN_SYSTEM_USER_TOKEN \
              --from-literal=META_WADE_ADS_PIXEL_ID=$META_WADE_ADS_PIXEL_ID \
              --from-literal=META_GABE_ADS_AD_COST_APP_ID=$META_GABE_ADS_AD_COST_APP_ID \
              --from-literal=META_GABE_ADS_AD_COST_APP_SECRET=$META_GABE_ADS_AD_COST_APP_SECRET \
              --from-literal=META_GABE_ADS_AD_COST_TOKEN=$META_GABE_ADS_AD_COST_TOKEN \
              --from-literal=META_WADE_ADS_AD_COST_APP_ID=$META_WADE_ADS_AD_COST_APP_ID \
              --from-literal=META_WADE_ADS_AD_COST_APP_SECRET=$META_WADE_ADS_AD_COST_APP_SECRET \
              --from-literal=META_WADE_ADS_AD_COST_TOKEN=$META_WADE_ADS_AD_COST_TOKEN \
              --from-literal=WATCHDOG_TWO_SERVER_URL=$WATCHDOG_TWO_SERVER_URL \
              --from-literal=WATCHDOG_TWO_PERSONAL_ACCESS_TOKEN=$WATCHDOG_TWO_PERSONAL_ACCESS_TOKEN \
              --from-literal=PRIVACY_ACCESS_TOKEN=$PRIVACY_ACCESS_TOKEN \
              --from-literal=SOLAR_ESTIMATE_DOMAIN=$SOLAR_ESTIMATE_DOMAIN \
              --from-literal=AFFILIATES_PORTAL_API_URL=$AFFILIATES_PORTAL_API_URL \
              --from-literal=AFFILIATES_PORTAL_API_TOKEN=$AFFILIATES_PORTAL_API_TOKEN \
              --from-literal=FLOW_ENGINE_URL=$FLOW_ENGINE_URL \
              --from-literal=EMAIL_MARKETING_API_KEY=$EMAIL_MARKETING_API_KEY \
              --from-literal=EMAIL_MARKETING_SERVER=$EMAIL_MARKETING_SERVER \
              --from-literal=EMAIL_MARKETING_DRIVER=$EMAIL_MARKETING_DRIVER \
              --from-literal=DIRECT_LEAD_ENABLED_INDUSTRY_IDS=$DIRECT_LEAD_ENABLED_INDUSTRY_IDS \
              --from-literal=CONTRACT_PROVIDER_INTEGRATION_KEY=$CONTRACT_PROVIDER_INTEGRATION_KEY \
              --from-literal=CONTRACT_PROVIDER_USER_ID=$CONTRACT_PROVIDER_USER_ID \
              --from-literal=CONTRACT_PROVIDER_ACCOUNT_ID=$CONTRACT_PROVIDER_ACCOUNT_ID \
              --from-literal=CONTRACT_PROVIDER_PRIVATE_KEY=$CONTRACT_PROVIDER_PRIVATE_KEY \
              --from-literal=EMAIL_MARKETING_SOCKET_LABS_SERVER=$EMAIL_MARKETING_SOCKET_LABS_SERVER \
              --from-literal=EMAIL_MARKETING_SOCKET_LABS_API_KEY=$EMAIL_MARKETING_SOCKET_LABS_API_KEY \
              --from-literal=SLACK_ADMIN_SYSTEM_BOT_AUTH_TOKEN=$SLACK_ADMIN_SYSTEM_BOT_AUTH_TOKEN \
              --from-literal=SLACK_COMPANY_REGISTRATION_BOT_AUTH_TOKEN=$SLACK_COMPANY_REGISTRATION_BOT_AUTH_TOKEN \
              --from-literal=SLACK_REGISTRATION_NOTIFICATION_CHANNEL_ID=$SLACK_REGISTRATION_NOTIFICATION_CHANNEL_ID \
              --from-literal=SALES_INTEL_API_KEY=$SALES_INTEL_API_KEY \
              --from-literal=GOOGLE_STORAGE_BASE_URL=$GOOGLE_STORAGE_BASE_URL \
              --from-literal=REVIEW_ATTACHMENTS_BUCKET=$REVIEW_ATTACHMENTS_BUCKET \
              --from-literal=EMAIL_MARKETING_SOCKET_LABS_WEBHOOK_SECRET=$EMAIL_MARKETING_SOCKET_LABS_WEBHOOK_SECRET \
              --from-literal=DEEPGRAM_API_KEY=$DEEPGRAM_API_KEY \
              --from-literal=EMAIL_MARKETING_SOCKET_LABS_API_KEY_SECONDARY=$EMAIL_MARKETING_SOCKET_LABS_API_KEY_SECONDARY \
              --from-literal=EMAIL_MARKETING_SOCKET_LABS_SERVER_SECONDARY=$EMAIL_MARKETING_SOCKET_LABS_SERVER_SECONDARY \
              --from-literal=SEMRUSH_API_KEY=$SEMRUSH_API_KEY \ 
              --from-literal=GOOGLE_SOLAR_API_KEY=$GOOGLE_SOLAR_API_KEY \
              --from-literal=SOLAR_CLASSIFIER_API_URL=$SOLAR_CLASSIFIER_API_URL \
              --from-literal=SOLAR_CLASSIFIER_API_KEY=$SOLAR_CLASSIFIER_API_KEY \
          - cp ./kubernetes/patch-secrets.yml ~/patch-secrets.yml
          - sed -i "s/sr-admin-example-secret/sr-admin-$DEPLOYMENT_NAME-secret/g" ~/patch-secrets.yml
          - kubectl patch deployment sr-admin-$DEPLOYMENT_NAME-deployment --patch "$(cat ~/patch-secrets.yml)"
          - kubectl patch deployment sr-admin-$DEPLOYMENT_NAME-webhook-deployment --patch "$(cat ~/patch-secrets.yml)"
          - kubectl patch deployment sr-admin-$DEPLOYMENT_NAME-horizon-deployment --patch "$(cat ~/patch-secrets.yml)"
          - kubectl patch deployment sr-admin-$DEPLOYMENT_NAME-cron-server-deployment --patch "$(cat ~/patch-secrets.yml)"
          - kubectl set image deployment/sr-admin-$DEPLOYMENT_NAME-deployment server=$IMAGE_NAME
          - kubectl set image deployment/sr-admin-$DEPLOYMENT_NAME-webhook-deployment server=$IMAGE_NAME
          - kubectl set image deployment/sr-admin-$DEPLOYMENT_NAME-horizon-deployment server=$HORIZON_WORKER_IMAGE_NAME
          - kubectl set image deployment/sr-admin-$DEPLOYMENT_NAME-cron-server-deployment server=$CRON_IMAGE_NAME
          - curl -sL https://sentry.io/get-cli/ | SENTRY_CLI_VERSION="2.0.4" bash
          - VERSION_NUMBER=$(sentry-cli --auth-token $SENTRY_AUTH_TOKEN releases propose-version)
          - BUILD_DATE=$(date +"%Y.%m.%d")
          - VERSION="$BUILD_DATE-$VERSION_NUMBER"
          - sentry-cli --auth-token $SENTRY_AUTH_TOKEN releases -o solar-investments new -p admin-20 "$VERSION"
          - sentry-cli --auth-token $SENTRY_AUTH_TOKEN releases set-commits --ignore-missing --auto -p admin-20 -o solar-investments "$VERSION"
          - sentry-cli --auth-token $SENTRY_AUTH_TOKEN releases -o solar-investments deploys "$VERSION" new -e $DEPLOYMENT_NAME
          - sentry-cli --auth-token $SENTRY_AUTH_TOKEN releases -o solar-investments finalize "$VERSION"
    - step: &build-base-image
        name: Builds the base image for our application
        image: google/cloud-sdk:latest
        services:
          - docker
        caches:
          - docker
        script:
          - echo "Creating Deployment"
          - echo $DEPLOYMENT_KEY_FILE | base64 -d > ~/.gcloud-api-key.json
          - export USE_GKE_GCLOUD_AUTH_PLUGIN=True
          - gcloud auth activate-service-account --key-file ~/.gcloud-api-key.json
          - gcloud config set project $DEPLOYMENT_PROJECT_ID
          - gcloud auth configure-docker --quiet
          - export IMAGE_NAME="gcr.io/$DEPLOYMENT_PROJECT_ID/base:$BITBUCKET_BUILD_NUMBER"
          - export LATEST_IMAGE_NAME="gcr.io/$DEPLOYMENT_PROJECT_ID/base:latest"
          - docker build -f Dockerfile.base-image --build-arg google_ads_service_account=$GOOGLE_OAUTH_SERVICE_ACCOUNT --build-arg github_auth_api_key=$GITHUB_API_TOKEN -t $IMAGE_NAME .
          - docker tag $IMAGE_NAME $LATEST_IMAGE_NAME
          - docker push $IMAGE_NAME
          - docker push $LATEST_IMAGE_NAME
pipelines:
  pull-requests:
    '**':
      - step:
          <<: *initialize-environment
          name: Initializing Environment Variables
          deployment: testing
      - step:
          <<: *run-tests
          name: Running Tests
  custom:
    update_base_image:
      - step:
          <<: *build-base-image
          name: Building base image
          deployment: base-image
    create_deployment:
      - variables:
          - name: DEPLOYMENT_NAME
          - name: CLUSTER_NAME
          - name: CLUSTER_ZONE
          - name: SUBDOMAIN_NAME
          - name: INITIAL_CONTAINER
          - name: INITIAL_QUEUE_CONTAINER
      - step:
          <<: *create-deployment
          name: Create Deployment
    development_release:
      - step:
          <<: *initialize-environment
          name: Initializing Environment Variables
          deployment: development
      - step:
          <<: *run-tests
          name: Running Tests
      - step:
          <<: *build-and-push
          name: Deploying to Development
    parallel_development_release:
      - step:
          <<: *initialize-environment
          name: Initializing Environment Variables
          deployment: development
      - parallel:
          - step:
              <<: *run-tests
              name: Running Tests
          - step:
              <<: *build-frontend
              name: Building Front End
          - step:
              <<: *build-horizon-worker
              name: Build Horizon Worker
          - step:
              <<: *build-cron-server
              name: Build Cron Server
      - step:
          <<: *deploy
          name: Deploying to Development
    parallel_staging_release:
      - step:
          <<: *initialize-environment
          name: Initializing Environment Variables
          deployment: staging
      - parallel:
          - step:
              <<: *run-tests
              name: Running Tests
          - step:
              <<: *build-frontend
              name: Building Front End
          - step:
              <<: *build-horizon-worker
              name: Build Horizon Worker
          - step:
              <<: *build-cron-server
              name: Build Cron Server
      - step:
          <<: *deploy
          name: Deploying to Staging
    parallel_production_release:
      - step:
          <<: *initialize-environment
          name: Initializing Environment Variables
          deployment: production
      - parallel:
          - step:
              <<: *build-frontend
              name: Building Front End
          - step:
              <<: *build-horizon-worker
              name: Build Horizon Worker
          - step:
              <<: *build-cron-server
              name: Build Cron Server
      - step:
          <<: *deploy
          trigger: manual
          name: Deploying to Production
    release:
      - step:
          <<: *initialize-environment
          name: Initializing Environment Variables
          deployment: development
      - step:
          <<: *run-tests
          name: Running Development Tests
      - step:
          <<: *build-and-push
          name: Deploying to Development
      - step:
          <<: *initialize-environment
          name: Initializing Environment Variables
          deployment: production
          trigger: manual
      - step:
          <<: *run-tests
          name: Running Production Tests
      - step:
          <<: *build-and-push
          name: Deploying to Production
          trigger: manual
    production_only_release:
      - step:
          <<: *initialize-environment
          name: Initializing Environment Variables
          deployment: production
      - step:
          <<: *run-tests
          name: Running Production Tests
      - step:
          <<: *build-and-push
          name: Deploying to Production
          trigger: manual
  branches:
    development:
      - step:
          <<: *initialize-environment
          name: Initializing Environment Variables
          deployment: development
      - parallel:
          - step:
              <<: *run-tests
              name: Running Tests
          - step:
              <<: *build-frontend
              name: Building Front End
          - step:
              <<: *build-horizon-worker
              name: Build Horizon Worker
          - step:
              <<: *build-cron-server
              name: Build Cron Server
      - step:
          <<: *deploy
          name: Deploying to Development
  tags:
    'v*.*.*':
      - step:
          <<: *initialize-environment
          name: Initializing Environment Variables
          deployment: production
      - parallel:
          - step:
              <<: *run-tests
              name: Running Tests
          - step:
              <<: *build-frontend
              name: Building Front End
          - step:
              <<: *build-horizon-worker
              name: Build Horizon Worker
          - step:
              <<: *build-cron-server
              name: Build Cron Server
      - step:
          <<: *deploy
          trigger: manual
          name: Deploying to Production
