spec:
  template:
    spec:
      containers:
        - name: server
          env:
            - name: APP_NAME
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: APP_NAME
            - name: APP_ENV
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: APP_ENV
            - name: MIX_APP_ENV
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: MIX_APP_ENV
            - name: APP_KEY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: APP_KEY
            - name: APP_DEBUG
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: APP_DEBUG
            - name: APP_URL
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: APP_URL
            - name: DB_CONNECTION
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: DB_CONNECTION
            - name: DB_HOST
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: DB_HOST
            - name: DB_PORT
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: DB_PORT
            - name: DB_DATABASE
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: DB_DATABASE
            - name: DB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: DB_USERNAME
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: DB_PASSWORD
            - name: REPLICA_DB_CONNECTION
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: REPLICA_DB_CONNECTION
            - name: REPLICA_DB_HOST
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: REPLICA_DB_HOST
            - name: REPLICA_TWO_DB_HOST
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: REPLICA_TWO_DB_HOST
            - name: REPLICA_DB_PORT
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: REPLICA_DB_PORT
            - name: REPLICA_DB_DATABASE
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: REPLICA_DB_DATABASE
            - name: REPLICA_DB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: REPLICA_DB_USERNAME
            - name: REPLICA_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: REPLICA_DB_PASSWORD
            - name: READONLY_DB_CONNECTION
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: READONLY_DB_CONNECTION
            - name: READONLY_DB_HOST
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: READONLY_DB_HOST
            - name: READONLY_DB_PORT
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: READONLY_DB_PORT
            - name: READONLY_DB_DATABASE
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: READONLY_DB_DATABASE
            - name: READONLY_DB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: READONLY_DB_USERNAME
            - name: READONLY_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: READONLY_DB_PASSWORD
            - name: SENTRY_DSN
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: SENTRY_DSN
            - name: MIX_SENTRY_DSN
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: MIX_SENTRY_DSN
            - name: SENTRY_TRACE_QUEUE_ENABLED
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: SENTRY_TRACE_QUEUE_ENABLED
            - name: SENTRY_SEND_DEFAULT_PII
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: SENTRY_SEND_DEFAULT_PII
            - name: SENTRY_TRACES_SAMPLE_RATE
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: SENTRY_TRACES_SAMPLE_RATE
            - name: QUEUE_CONNECTION
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: QUEUE_CONNECTION
            - name: REDIS_CLIENT
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: REDIS_CLIENT
            - name: REDIS_HOST
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: REDIS_HOST
            - name: REDIS_QUEUE_HOST
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: REDIS_QUEUE_HOST
            - name: REDIS_QUEUE_PORT
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: REDIS_QUEUE_PORT
            - name: SESSION_DRIVER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: SESSION_DRIVER
            - name: TWILIO_ML_SID
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: TWILIO_ML_SID
            - name: TWILIO_SID
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: TWILIO_SID
            - name: TWILIO_TOKEN
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: TWILIO_TOKEN
            - name: TWILIO_REVIEW_REQUESTS
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: TWILIO_REVIEW_REQUESTS
            - name: COMMUNICATION_DRIVER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: COMMUNICATION_DRIVER
            - name: LEAD_COMMUNICATION_DRIVER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: LEAD_COMMUNICATION_DRIVER
            - name: PUSHER_APP_ID
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: PUSHER_APP_ID
            - name: PUSHER_APP_KEY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: PUSHER_APP_KEY
            - name: PUSHER_APP_SECRET
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: PUSHER_APP_SECRET
            - name: PUSHER_APP_CLUSTER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: PUSHER_APP_CLUSTER
            - name: BROADCAST_DRIVER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: BROADCAST_DRIVER
            - name: CACHE_DRIVER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: CACHE_DRIVER
            - name: ADMIN_INTEGRATION_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: ADMIN_INTEGRATION_CLIENT_SECRET
            - name: ADMIN_INTEGRATION_BASE_URL
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: ADMIN_INTEGRATION_BASE_URL
            - name: LEAD_PROCESSING_API_DRIVER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: LEAD_PROCESSING_API_DRIVER
            - name: SALES_MANAGEMENT_API_DRIVER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: SALES_MANAGEMENT_API_DRIVER
            - name: USER_MANAGEMENT_API_DRIVER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: USER_MANAGEMENT_API_DRIVER
            - name: MAIL_MAILER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: MAIL_MAILER
            - name: MAIL_HOST
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: MAIL_HOST
            - name: MAIL_PORT
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: MAIL_PORT
            - name: MAIL_USERNAME
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: MAIL_USERNAME
            - name: MAIL_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: MAIL_PASSWORD
            - name: MAIL_USERNAME_SECONDARY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: MAIL_USERNAME_SECONDARY
            - name: MAIL_PASSWORD_SECONDARY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: MAIL_PASSWORD_SECONDARY
            - name: MAIL_USERNAME_TERTIARY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: MAIL_USERNAME_TERTIARY
            - name: MAIL_PASSWORD_TERTIARY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: MAIL_PASSWORD_TERTIARY
            - name: MAIL_FROM_ADDRESS
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: MAIL_FROM_ADDRESS
            - name: MAIL_FROM_NAME
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: MAIL_FROM_NAME
            - name: SOLAR_MAIL_FROM_ADDRESS
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: SOLAR_MAIL_FROM_ADDRESS
            - name: SOLAR_MAIL_FROM_NAME
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: SOLAR_MAIL_FROM_NAME
            - name: ROOFING_MAIL_FROM_ADDRESS
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: ROOFING_MAIL_FROM_ADDRESS
            - name: ROOFING_MAIL_FROM_NAME
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: ROOFING_MAIL_FROM_NAME
            - name: LEGACY_ADMIN_AUTH_TOKEN
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: LEGACY_ADMIN_AUTH_TOKEN
            - name: GOOGLE_STATIC_MAPS_API
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_STATIC_MAPS_API
            - name: GOOGLE_STATIC_MAPS_API_KEY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_STATIC_MAPS_API_KEY
            - name: GOOGLE_MAPS_GEOCODING_API_KEY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_MAPS_GEOCODING_API_KEY
            - name: GOOGLE_GMAIL_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_GMAIL_CLIENT_ID
            - name: GOOGLE_STORAGE_MAILBOX_BUCKET
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_STORAGE_MAILBOX_BUCKET
            - name: GOOGLE_STORAGE_MAILBOX_CDN_URL
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_STORAGE_MAILBOX_CDN_URL
            - name: GOOGLE_GMAIL_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_GMAIL_CLIENT_SECRET
            - name: MAILBOX_MAIL_PROVIDER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: MAILBOX_MAIL_PROVIDER
            - name: MAILBOX_API_DRIVER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: MAILBOX_API_DRIVER
            - name: GOOGLE_GMAIL_EMAIL_LISTENER_TOPIC
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_GMAIL_EMAIL_LISTENER_TOPIC
            - name: GOOGLE_CLOUD_KEY_FILE
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_CLOUD_KEY_FILE
            - name: GOOGLE_STORAGE_INVOICES_BUCKET
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_STORAGE_INVOICES_BUCKET
            - name: PAYMENT_GATEWAY_DRIVER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: PAYMENT_GATEWAY_DRIVER
            - name: STRIPE_WEBHOOK_SIGNING_SECRET
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: STRIPE_WEBHOOK_SIGNING_SECRET
            - name: OUTGOING_COMMUNICATION_TEST_MODE
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: OUTGOING_COMMUNICATION_TEST_MODE
            - name: MIX_LEGACY_ADMIN_BASE_URL
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: MIX_LEGACY_ADMIN_BASE_URL
            - name: WWW_SOLARREVIEWS_DOMAIN
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: WWW_SOLARREVIEWS_DOMAIN
            - name: GOOGLE_LOGGING_PROJECT_ID
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_LOGGING_PROJECT_ID
            - name: GOOGLE_PUBSUB_PROJECT_ID
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_PUBSUB_PROJECT_ID
            - name: GOOGLE_PUBSUB_TOPIC
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_PUBSUB_TOPIC
            - name: GOOGLE_STORAGE_PROJECT_ID
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_STORAGE_PROJECT_ID
            - name: GOOGLE_STORAGE_EMAIL_TEMPLATE_IMAGES_BUCKET
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_STORAGE_EMAIL_TEMPLATE_IMAGES_BUCKET
            - name: STACKDRIVER_ENABLED
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: STACKDRIVER_ENABLED
            - name: GOOGLE_ADS_DEVELOPER_TOKEN
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_ADS_DEVELOPER_TOKEN
            - name: GOOGLE_ADS_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_ADS_CLIENT_ID
            - name: GOOGLE_ADS_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_ADS_CLIENT_SECRET
            - name: GOOGLE_ADS_REFRESH_TOKEN
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_ADS_REFRESH_TOKEN
            - name: GOOGLE_ADS_GEOTARGETS_CSV_URL
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_ADS_GEOTARGETS_CSV_URL
            - name: GOOGLE_ADS_IMPERSONATED_EMAIL
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_ADS_IMPERSONATED_EMAIL
            - name: GOOGLE_ADS_AUTH_DRIVER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_ADS_AUTH_DRIVER
            - name: ADVERTISING_DRIVER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: ADVERTISING_DRIVER
            - name: CLIENT_JWT_SIGNING_KEY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: CLIENT_JWT_SIGNING_KEY
            - name: ADDRESS_IDENTIFICATION_DRIVER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: ADDRESS_IDENTIFICATION_DRIVER
            - name: GOOGLE_MAPS_PLACES_API_URI
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_MAPS_PLACES_API_URI
            - name: GOOGLE_ADS_CONVERSION_ACTION_NAME
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_ADS_CONVERSION_ACTION_NAME
            - name: IP_QUALITY_SCORE_BASE_URL
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: IP_QUALITY_SCORE_BASE_URL
            - name: IP_QUALITY_SCORE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: IP_QUALITY_SCORE_API_KEY
            - name: IP_QUALITY_SCORE_DRIVER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: IP_QUALITY_SCORE_DRIVER
            - name: DATABASE_CONNECTION_RETRY_AFTER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: DATABASE_CONNECTION_RETRY_AFTER
            - name: WHITEPAGES_IDENTITY_VERIFICATION_KEY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: WHITEPAGES_IDENTITY_VERIFICATION_KEY
            - name: WHITEPAGES_IDENTITY_VERIFICATION_URL
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: WHITEPAGES_IDENTITY_VERIFICATION_URL
            - name: CONSUMER_PRODUCT_VERIFICATION_DRIVER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: CONSUMER_PRODUCT_VERIFICATION_DRIVER
            - name: MICROSOFT_ADS_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: MICROSOFT_ADS_CLIENT_ID
            - name: MICROSOFT_ADS_OAUTH_REDIRECT_URI
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: MICROSOFT_ADS_OAUTH_REDIRECT_URI
            - name: MICROSOFT_ADS_CUSTOMER_ID
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: MICROSOFT_ADS_CUSTOMER_ID
            - name: MICROSOFT_ADS_OFFLINE_CONVERSION_GOAL_NAME
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: MICROSOFT_ADS_OFFLINE_CONVERSION_GOAL_NAME
            - name: MICROSOFT_ADS_DEVELOPER_TOKEN
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: MICROSOFT_ADS_DEVELOPER_TOKEN
            - name: MICROSOFT_ADS_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: MICROSOFT_ADS_CLIENT_SECRET
            - name: META_ADS_APP_ID
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: META_ADS_APP_ID
            - name: META_ADS_APP_SECRET
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: META_ADS_APP_SECRET
            - name: META_ADS_BUSINESS_ID
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: META_ADS_BUSINESS_ID
            - name: META_ADS_ADMIN_SYSTEM_USER_TOKEN
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: META_ADS_ADMIN_SYSTEM_USER_TOKEN
            - name: TINY_MCE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: TINY_MCE_API_KEY
            - name: TIME_MCE_STORAGE_DISK
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: TIME_MCE_STORAGE_DISK
            - name: GOOGLE_STORAGE_TINYMCE_FILES_BUCKET
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_STORAGE_TINYMCE_FILES_BUCKET
            - name: GOOGLE_STORAGE_TINYMCE_FILE_URL
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_STORAGE_TINYMCE_FILE_URL
            - name: ADS_NOTIFICATION_EMAILS
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: ADS_NOTIFICATION_EMAILS
            - name: CONSUMER_PRODUCT_VERIFICATION_DRIVER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: CONSUMER_PRODUCT_VERIFICATION_DRIVER
            - name: HUNTER_PRIORITY_CALCULATION_UNSOLD_DAYS
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: HUNTER_PRIORITY_CALCULATION_UNSOLD_DAYS
            - name: FIXR_DOMAIN
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: FIXR_DOMAIN
            - name: FIXR_ORIGIN_DOMAIN
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: FIXR_ORIGIN_DOMAIN
            - name: AUTH_TOKEN_SINGING_KEY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: AUTH_TOKEN_SINGING_KEY
            - name: TWILIO_FROM_PHONE_NUMBER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: TWILIO_FROM_PHONE_NUMBER
            - name: TWILIO_CONSUMER_VERIFICATION_FROM_PHONE
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: TWILIO_CONSUMER_VERIFICATION_FROM_PHONE
            - name: TWILIO_RECYCLED_LEADS_FROM_PHONE
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: TWILIO_RECYCLED_LEADS_FROM_PHONE
            - name: COMPANY_DISCOVERY_GOOGLE_PLACES_API_KEY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: COMPANY_DISCOVERY_GOOGLE_PLACES_API_KEY
            - name: DASHBOARD_CLIENT_API_JWT_SIGNING_KEY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: DASHBOARD_CLIENT_API_JWT_SIGNING_KEY
            - name: DASHBOARD_CLIENT_API_JWT_EXPIRE_IN
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: DASHBOARD_CLIENT_API_JWT_EXPIRE_IN
            - name: LEAD_MINIMUM_ELECTRIC_SPEND
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: LEAD_MINIMUM_ELECTRIC_SPEND
            - name: LEAD_REJECTION_PERCENTAGE_THRESHOLD
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: LEAD_REJECTION_PERCENTAGE_THRESHOLD
            - name: LEAD_CRM_REJECTION_PERCENTAGE_THRESHOLD
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: LEAD_CRM_REJECTION_PERCENTAGE_THRESHOLD
            - name: APPT_MINIMUM_ELECTRIC_SPEND
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: APPT_MINIMUM_ELECTRIC_SPEND
            - name: APPT_REJECTION_PERCENTAGE_THRESHOLD
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: APPT_REJECTION_PERCENTAGE_THRESHOLD
            - name: APPT_CRM_REJECTION_PERCENTAGE_THRESHOLD
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: APPT_CRM_REJECTION_PERCENTAGE_THRESHOLD
            - name: MAX_CHARGE_ATTEMPTS
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: MAX_CHARGE_ATTEMPTS
            - name: APPT_OFFERING_AVAILABLE_DURATION_MIN
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: APPT_OFFERING_AVAILABLE_DURATION_MIN
            - name: APPTS_SOLARREVIEWS_DOMAIN
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: APPTS_SOLARREVIEWS_DOMAIN
            - name: APPT_MAX_OFFERING_ATTEMPTS
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: APPT_MAX_OFFERING_ATTEMPTS
            - name: APPT_REJECTION_WINDOW_DURATION_HOURS
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: APPT_REJECTION_WINDOW_DURATION_HOURS
            - name: BRS_DRIVER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: BRS_DRIVER
            - name: APPT_CONSUMER_ADVANCE_NOTICE_HOURS
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: APPT_CONSUMER_ADVANCE_NOTICE_HOURS
            - name: PRODUCT_PRICING_DRIVER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: PRODUCT_PRICING_DRIVER
            - name: LEAD_REJECTION_WINDOW_DURATION_HOURS
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: LEAD_REJECTION_WINDOW_DURATION_HOURS
            - name: STRIPE_LIVE
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: STRIPE_LIVE
            - name: STRIPE_API_VERSION
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: STRIPE_API_VERSION
            - name: STRIPE_API_KEY_SECRET_TEST
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: STRIPE_API_KEY_SECRET_TEST
            - name: STRIPE_API_KEY_SECRET_LIVE
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: STRIPE_API_KEY_SECRET_LIVE
            - name: STRIPE_API_KEY_PUBLISHABLE_TEST
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: STRIPE_API_KEY_PUBLISHABLE_TEST
            - name: STRIPE_API_KEY_PUBLISHABLE_LIVE
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: STRIPE_API_KEY_PUBLISHABLE_LIVE
            - name: LOG_SLACK_WEBHOOK_URL
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: LOG_SLACK_WEBHOOK_URL
            - name: APPT_LOG_BRS_QUERIES
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: APPT_LOG_BRS_QUERIES
            - name: LOG_JOB_EVENTS
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: LOG_JOB_EVENTS
            - name: GOOGLE_FIRESTORE_PROJECT_ID
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_FIRESTORE_PROJECT_ID
            - name: GOOGLE_FIRESTORE_KEY_FILE
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_FIRESTORE_KEY_FILE
            - name: GOOGLE_FIRESTORE_COLLECTION_PREFIX
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_FIRESTORE_COLLECTION_PREFIX
            - name: FLOW_BUILDER_IMAGE_BUCKET
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: FLOW_BUILDER_IMAGE_BUCKET
            - name: FLOW_BUILDER_CDN_URL
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: FLOW_BUILDER_CDN_URL
            - name: FLOW_BUILDER_URL
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: FLOW_BUILDER_URL
            - name: FLOW_CLIENT_URL
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: FLOW_CLIENT_URL
            - name: SCHEDULING_BASE_API_URL
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: SCHEDULING_BASE_API_URL
            - name: SCHEDULING_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: SCHEDULING_CLIENT_SECRET
            - name: ALLOCATE_DIRECT_LEADS_WITHOUT_TIMEZONE_OPEN_DELAY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: ALLOCATE_DIRECT_LEADS_WITHOUT_TIMEZONE_OPEN_DELAY
            - name: DATABASE_QUEUE_AFTER_COMMIT
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: DATABASE_QUEUE_AFTER_COMMIT
            - name: APPT_NEXT_DELIVERY_ATTEMPT_BUFFER_MINUTES
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: APPT_NEXT_DELIVERY_ATTEMPT_BUFFER_MINUTES
            - name: APPT_MAX_DELIVERY_ATTEMPTS
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: APPT_MAX_DELIVERY_ATTEMPTS
            - name: GOOGLE_ADS_LOGIN_CUSTOMER_ID
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_ADS_LOGIN_CUSTOMER_ID
            - name: URL_SIGNER_SIGNATURE_KEY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: URL_SIGNER_SIGNATURE_KEY
            - name: OPPORTUNITY_NOTIFICATIONS_CRON
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: OPPORTUNITY_NOTIFICATIONS_CRON
            - name: GOOGLE_DEBUG_MONITORING_LOGS
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_DEBUG_MONITORING_LOGS
            - name: APPT_SELL_APPT_AS_LEAD_BUFFER_AFTER_MIN
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: APPT_SELL_APPT_AS_LEAD_BUFFER_AFTER_MIN
            - name: FLOW_PROXY_URL
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: FLOW_PROXY_URL
            - name: FLOW_PROXY_SECRET
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: FLOW_PROXY_SECRET
            - name: FILTERS_API_DRIVER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: FILTERS_API_DRIVER
            - name: RUN_SCHEDULED_ADS_JOBS
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: RUN_SCHEDULED_ADS_JOBS
            - name: MAILSLURP_API_KEY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: MAILSLURP_API_KEY
            - name: SOLAR_REVIEWS_FRONTEND_DOMAIN
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: SOLAR_REVIEWS_FRONTEND_DOMAIN
            - name: FLOW_BUILDER_DEFAULT_SAFE_CALL_IDS
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: FLOW_BUILDER_DEFAULT_SAFE_CALL_IDS
            - name: ARE_UTILITY_FILTERS_ACTIVE
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: ARE_UTILITY_FILTERS_ACTIVE
            - name: COMPANY_SEARCH_FILTER_PURCHASING_FROM_COMPETITOR_QUERY_STRATEGY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: COMPANY_SEARCH_FILTER_PURCHASING_FROM_COMPETITOR_QUERY_STRATEGY
            - name: DELIVERY_FAILURE_NOTIFICATION_EMAILS
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: DELIVERY_FAILURE_NOTIFICATION_EMAILS
            - name: DROPBOX_API_KEY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: DROPBOX_API_KEY
            - name: MIX_DROPBOX_ACCOUNT_ID
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: MIX_DROPBOX_ACCOUNT_ID
            - name: TWILIO_VERIFY_SERVICE_SID
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: TWILIO_VERIFY_SERVICE_SID
            - name: COMPANY_METRICS_DRIVER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: COMPANY_METRICS_DRIVER
            - name: SIMILAR_WEB_API_BASE_URL
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: SIMILAR_WEB_API_BASE_URL
            - name: SIMILAR_WEB_API_KEY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: SIMILAR_WEB_API_KEY
            - name: SPY_FU_API_BASE_URL
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: SPY_FU_API_BASE_URL
            - name: SPY_FU_API_KEY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: SPY_FU_API_KEY
            - name: SPY_FU_API_ID
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: SPY_FU_API_ID
            - name: GOOGLE_STORAGE_CONTRACT_BUCKET
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_STORAGE_CONTRACT_BUCKET
            - name: GOOGLE_STORAGE_CONTRACT_URL
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_STORAGE_CONTRACT_URL
            - name: LARGE_ACCOUNT_REVENUE_THRESHOLD
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: LARGE_ACCOUNT_REVENUE_THRESHOLD
            - name: STAGING_TESTING_PUBSUB_TOPIC
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: STAGING_TESTING_PUBSUB_TOPIC
            - name: FUTURE_ALLOCATION_TESTING_INDUSTRY_ID
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: FUTURE_ALLOCATION_TESTING_INDUSTRY_ID
            - name: META_ADS_AUTOMATION_SYSTEM_USER_NAME
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: META_ADS_AUTOMATION_SYSTEM_USER_NAME
            - name: OMIT_LOW_NEVER_EXCEED_BUDGET
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: OMIT_LOW_NEVER_EXCEED_BUDGET
            - name: META_WADE_ADS_APP_ID
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: META_WADE_ADS_APP_ID
            - name: META_WADE_ADS_APP_SECRET
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: META_WADE_ADS_APP_SECRET
            - name: META_WADE_ADS_BUSINESS_ID
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: META_WADE_ADS_BUSINESS_ID
            - name: META_WADE_ADS_ADMIN_SYSTEM_USER_TOKEN
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: META_WADE_ADS_ADMIN_SYSTEM_USER_TOKEN
            - name: META_WADE_ADS_PIXEL_ID
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: META_WADE_ADS_PIXEL_ID
            - name: META_GABE_ADS_AD_COST_APP_ID
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: META_GABE_ADS_AD_COST_APP_ID
            - name: META_GABE_ADS_AD_COST_APP_SECRET
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: META_GABE_ADS_AD_COST_APP_SECRET
            - name: META_GABE_ADS_AD_COST_TOKEN
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: META_GABE_ADS_AD_COST_TOKEN
            - name: META_WADE_ADS_AD_COST_APP_ID
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: META_WADE_ADS_AD_COST_APP_ID
            - name: META_WADE_ADS_AD_COST_APP_SECRET
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: META_WADE_ADS_AD_COST_APP_SECRET
            - name: META_WADE_ADS_AD_COST_TOKEN
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: META_WADE_ADS_AD_COST_TOKEN
            - name: WATCHDOG_TWO_SERVER_URL
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: WATCHDOG_TWO_SERVER_URL
            - name: WATCHDOG_TWO_PERSONAL_ACCESS_TOKEN
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: WATCHDOG_TWO_PERSONAL_ACCESS_TOKEN
            - name: PRIVACY_ACCESS_TOKEN
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: PRIVACY_ACCESS_TOKEN
            - name: SOLAR_ESTIMATE_DOMAIN
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: SOLAR_ESTIMATE_DOMAIN
            - name: AFFILIATES_PORTAL_API_TOKEN
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: AFFILIATES_PORTAL_API_TOKEN
            - name: AFFILIATES_PORTAL_API_URL
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: AFFILIATES_PORTAL_API_URL
            - name: FLOW_ENGINE_URL
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: FLOW_ENGINE_URL
            - name: EMAIL_MARKETING_API_KEY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: EMAIL_MARKETING_API_KEY
            - name: EMAIL_MARKETING_SERVER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: EMAIL_MARKETING_SERVER
            - name: EMAIL_MARKETING_DRIVER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: EMAIL_MARKETING_DRIVER
            - name: DIRECT_LEAD_ENABLED_INDUSTRY_IDS
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: DIRECT_LEAD_ENABLED_INDUSTRY_IDS
            - name: CONTRACT_PROVIDER_INTEGRATION_KEY
              valueFrom:
               secretKeyRef:
                 name: sr-admin-example-secret
                 key: CONTRACT_PROVIDER_INTEGRATION_KEY
            - name: CONTRACT_PROVIDER_USER_ID
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: CONTRACT_PROVIDER_USER_ID
            - name: CONTRACT_PROVIDER_ACCOUNT_ID
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: CONTRACT_PROVIDER_ACCOUNT_ID
            - name: CONTRACT_PROVIDER_PRIVATE_KEY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: CONTRACT_PROVIDER_PRIVATE_KEY
            - name: EMAIL_MARKETING_SOCKET_LABS_SERVER
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: EMAIL_MARKETING_SOCKET_LABS_SERVER
            - name: EMAIL_MARKETING_SOCKET_LABS_API_KEY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: EMAIL_MARKETING_SOCKET_LABS_API_KEY
            - name: SLACK_ADMIN_SYSTEM_BOT_AUTH_TOKEN
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: SLACK_ADMIN_SYSTEM_BOT_AUTH_TOKEN
            - name: SLACK_COMPANY_REGISTRATION_BOT_AUTH_TOKEN
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: SLACK_COMPANY_REGISTRATION_BOT_AUTH_TOKEN
            - name: SLACK_REGISTRATION_NOTIFICATION_CHANNEL_ID
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: SLACK_REGISTRATION_NOTIFICATION_CHANNEL_ID
            - name: SALES_INTEL_API_KEY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: SALES_INTEL_API_KEY
            - name: GOOGLE_STORAGE_BASE_URL
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_STORAGE_BASE_URL
            - name: REVIEW_ATTACHMENTS_BUCKET
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: REVIEW_ATTACHMENTS_BUCKET
            - name: EMAIL_MARKETING_SOCKET_LABS_WEBHOOK_SECRET
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: EMAIL_MARKETING_SOCKET_LABS_WEBHOOK_SECRET
            - name: DEEPGRAM_API_KEY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: DEEPGRAM_API_KEY
            - name: EMAIL_MARKETING_SOCKET_LABS_API_KEY_SECONDARY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: EMAIL_MARKETING_SOCKET_LABS_API_KEY_SECONDARY
            - name: EMAIL_MARKETING_SOCKET_LABS_SERVER_SECONDARY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: EMAIL_MARKETING_SOCKET_LABS_SERVER_SECONDARY
            - name: SEMRUSH_API_KEY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: SEMRUSH_API_KEY
            - name: GOOGLE_SOLAR_API_KEY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: GOOGLE_SOLAR_API_KEY
            - name: SOLAR_CLASSIFIER_API_URL
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: SOLAR_CLASSIFIER_API_URL
            - name: SOLAR_CLASSIFIER_API_KEY
              valueFrom:
                secretKeyRef:
                  name: sr-admin-example-secret
                  key: SOLAR_CLASSIFIER_API_KEY
