<?php

namespace Tests\Feature\CompanyLinkService;

use App\Models\Odin\Company;
use App\Models\Prospects\NewBuyerProspect;
use App\Services\Companies\CompanyLinkService;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ServiceTest extends TestCase
{
    protected CompanyLinkService $service;

    protected function setUp(): void
    {
        parent::setUp();

        $this->service = app(CompanyLinkService::class);
    }

    #[Test]
    public function company_has_no_prospects()
    {
        $company = Company::factory()->createQuietly();

        $url = $this->service->getDashboardUrl(company: $company);

        $this->assertEquals(config('app.url').'/companies/'.$company->id, $url);
    }

    #[Test]
    public function company_has_prospect()
    {
        $company = Company::factory()->createQuietly();

        $prospect = NewBuyerProspect::factory()->for($company)->createQuietly();

        $url = $this->service->getDashboardUrl(company: $company);

        $this->assertEquals(config('app.url').'/companies/'.$company->id, $url);
    }

    #[Test]
    public function company_has_prospects()
    {
        $company = Company::factory()->createQuietly();

        $prospects = NewBuyerProspect::factory()->count(3)->for($company)->createQuietly();

        $prospect = $prospects->last();

        $url = $this->service->getDashboardUrl(company: $company);

        $this->assertEquals(config('app.url').'/companies/'.$company->id, $url);
    }
}
