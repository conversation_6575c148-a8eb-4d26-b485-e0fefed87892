<?php

namespace Feature\Services;

use App\DataModels\Odin\ConfigurableFieldDataModel;
use App\Enums\GlobalConfigurationKey;
use App\Models\GlobalConfiguration;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyConfiguration;
use App\Models\Odin\Consumer;
use App\Models\Odin\ProductAssignment;
use App\Models\Phone;
use App\Services\Odin\ConsumerProxyPhoneService;
use App\Services\Twilio\TwilioPhoneNumberService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ConsumerProxyPhoneServiceTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        $this->creatGlobalConfiguration();
    }

    #[Test]
    public function it_gets_proxy_phone_if_enabled_for_company_and_state_is_allowed()
    {
        $company = $this->createCompany();
        $phone = $this->createPhone();
        $mockService = Mockery::mock(TwilioPhoneNumberService::class);

        $this->createCompanyConfiguration($company, [CompanyConfiguration::FIELD_CONSUMER_PROXY_PHONE_ENABLED => true]);
        $this->createProductAssignments($company);

        $mockService->shouldReceive('acquireNumber')
            ->once()
            ->andReturn($phone);

        $this->app->instance(TwilioPhoneNumberService::class, $mockService);

        $phoneNumber = app(ConsumerProxyPhoneService::class)->getProxyPhone($company->id, 'AZ');

        $this->assertEquals($phone->phone, $phoneNumber);
    }

    #[Test]
    public function it_does_not_get_proxy_phone_if_disabled_for_company()
    {
        $company = $this->createCompany();
        $mockService = Mockery::mock(TwilioPhoneNumberService::class);

        $this->createCompanyConfiguration($company, [CompanyConfiguration::FIELD_CONSUMER_PROXY_PHONE_ENABLED => false]);

        $mockService->shouldNotReceive('acquireNumber');

        $this->app->instance(TwilioPhoneNumberService::class, $mockService);

        $phone = app(ConsumerProxyPhoneService::class)->getProxyPhone($company->id, 'AZ');

        $this->assertEquals(null, $phone);
    }

    #[Test]
    public function it_does_not_get_proxy_phone_if_state_is_excluded()
    {
        $company = $this->createCompany();
        $mockService = Mockery::mock(TwilioPhoneNumberService::class);

        $this->createCompanyConfiguration($company, [CompanyConfiguration::FIELD_CONSUMER_PROXY_PHONE_ENABLED => true]);
        $this->createProductAssignments($company);

        $mockService->shouldNotReceive('acquireNumber');

        $this->app->instance(TwilioPhoneNumberService::class, $mockService);

        $phoneNumber = app(ConsumerProxyPhoneService::class)->getProxyPhone($company->id, 'CA');

        $this->assertEquals(null, $phoneNumber);
    }

    protected function creatGlobalConfiguration(): void
    {
        GlobalConfiguration::createQuietly([
            GlobalConfiguration::FIELD_CONFIGURATION_KEY => GlobalConfigurationKey::CONSUMER_PROXY_PHONE,
            GlobalConfiguration::FIELD_CONFIGURATION_PAYLOAD => ConfigurableFieldDataModel::fromArray([
                ConsumerProxyPhoneService::PROXY_PHONE_PERCENT => 5,
                ConsumerProxyPhoneService::STATES_EXCLUDED => 'CA,FL'
            ])
        ]);
    }

    protected function createProductAssignments(Company $company): void
    {
        $nthLead = (int) (100 / Consumer::PROXY_PHONE_DEFAULT_PERCENT);

        ProductAssignment::factory()->count($nthLead)->createQuietly([
            ProductAssignment::FIELD_COMPANY_ID => $company->id,
            ProductAssignment::FIELD_DELIVERED_AT => now(),
            ProductAssignment::FIELD_CHARGEABLE => true,
            ProductAssignment::FIELD_DELIVERED => true
        ]);
    }

    protected function createCompany(): Company
    {
        return Company::factory()->createQuietly();
    }

    protected function createPhone(): Phone
    {
        return Phone::factory()->createQuietly();
    }

    protected function createCompanyConfiguration(Company $company, array $data): void
    {
        $company->configuration()->createQuietly($data);
    }
}
