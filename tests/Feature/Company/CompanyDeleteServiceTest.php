<?php

namespace Feature\Company;

use App\Company\DeleteCompany\Deletes\DeleteCompany;
use App\Company\DeleteCompany\Deletes\DeleteContract;
use App\Enums\PermissionType;
use App\Enums\RoleType;
use App\Models\Action;
use App\Models\ActionTag;
use App\Models\Billing\CampaignBillingProfile;
use App\Models\Billing\CreditType;
use App\Models\Campaigns\Modules\Bidding\CompanyCampaignBidPriceModule;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryLog;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModule;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleCRM;
use App\Models\CompanyCampaignData;
use App\Models\ComputedRejectionStatistic;
use App\Models\ConsumerReviews\CompanyRating;
use App\Models\LeadRefund;
use App\Models\MissedProducts\OpportunityNotificationConfig;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\Odin\OptInCompany;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\SingleProductSale;
use App\Models\Permission;
use App\Models\Territory\CustomerManagerCompany;
use App\Models\Territory\CustomerSuccessManager;
use App\Models\TestProduct;
use App\Models\User;
use App\Services\Companies\Delete\CompanyDeleteService;
use Database\Seeders\ProductsSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Mockery;
use Spatie\EventSourcing\Projections\Projection;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class CompanyDeleteServiceTest extends TestCase
{
    use RefreshDatabase;

    protected function setOtherFields(string $model, Company $company): array
    {
        return match ($model) {
            Company::class                          => [
                Company::FIELD_ID => $company->id,
            ],
            Action::class                           => [
                Action::FIELD_FOR_RELATION_TYPE => Action::RELATION_TYPE_COMPANY,
                Action::FIELD_FOR_ID            => $company->id,
            ],
            ActionTag::class                        => [
                ActionTag::FIELD_ACTION_ID => $company->actions()->first()->id,
                ActionTag::FIELD_USER_ID   => Auth::user()->id,
            ],
            CompanyCampaignDeliveryModuleCRM::class => [
                CompanyCampaignDeliveryModuleCRM::FIELD_TEMPLATE_ID => $company->crmTemplates()->first()->id,
                CompanyCampaignDeliveryModuleCRM::FIELD_MODULE_ID   => $company->refresh()->futureCampaigns()->first()->deliveryModule->id,
            ],
            CompanyCampaignData::class              => [
                CompanyCampaignData::FIELD_CAMPAIGN_ID => $company->futureCampaigns()->first()->id,
            ],
            CompanyCampaignBidPriceModule::class    => [
                CompanyCampaignBidPriceModule::FIELD_CAMPAIGN_ID => $company->futureCampaigns()->first()->id,
            ],
            CompanyCampaignDeliveryModule::class    => [
                CompanyCampaignDeliveryModule::FIELD_CAMPAIGN_ID => $company->futureCampaigns()->first()->id,
            ],
            CompanyCampaignDeliveryLog::class       => [
                CompanyCampaignDeliveryLog::FIELD_CAMPAIGN_ID => $company->futureCampaigns()->first()->id,
            ],
            TestProduct::class                      => [
                TestProduct::FIELD_CREATED_BY_ID => Auth::user()->id,
                TestProduct::FIELD_RELATION_ID   => $company->futureCampaigns()->first()->id,
                TestProduct::FIELD_RELATION_TYPE => $company->futureCampaigns()->first()::class,
                TestProduct::FIELD_COMPANY_ID    => $company->id,
                TestProduct::FIELD_CAMPAIGN_ID   => $company->productCampaigns()->first()->id,
            ],
            ProductAssignment::class                => [
                ProductAssignment::FIELD_COMPANY_ID          => $company->id,
                ProductAssignment::FIELD_CAMPAIGN_ID         => $company->futureCampaigns()->first()->id,
                ProductAssignment::FIELD_CONSUMER_PRODUCT_ID => ConsumerProduct::query()->first()->id,
            ],
            SingleProductSale::class                => [
                SingleProductSale::FIELD_COMPANY_ID            => $company->id,
                SingleProductSale::FIELD_CONSUMER_PRODUCT_ID   => ConsumerProduct::query()->first()->id,
                SingleProductSale::FIELD_PRODUCT_ASSIGNMENT_ID => ProductAssignment::query()->where(ProductAssignment::FIELD_COMPANY_ID, $company->id)->first()->id,
            ],
            OptInCompany::class                     => [
                OptInCompany::FIELD_COMPANY_ID          => $company->id,
                OptInCompany::FIELD_CONSUMER_PRODUCT_ID => ConsumerProduct::query()->first()->id,
                OptInCompany::FIELD_COMPANY_CAMPAIGN_ID => $company->futureCampaigns()->first()->id,
            ],
            LeadRefund::class                       => [
                LeadRefund::FIELD_COMPANY_ID   => $company->id,
                LeadRefund::FIELD_REVIEWED_BY  => Auth::user()->id,
                LeadRefund::FIELD_REQUESTED_BY => Auth::user()->id,
            ],
            CustomerManagerCompany::class           => [
                CustomerManagerCompany::FIELD_COMPANY_ID                  => $company->id,
                CustomerManagerCompany::FIELD_CUSTOMER_SUCCESS_MANAGER_ID => CustomerSuccessManager::query()->first()->id,
            ],
            ComputedRejectionStatistic::class       => [
                ComputedRejectionStatistic::FIELD_COMPANY_ID => $company->id,
                ComputedRejectionStatistic::FIELD_PRODUCT_ID => Product::query()->first()->id,
            ],
            CompanyRating::class                    => [
                CompanyRating::FIELD_COMPANY_ID          => $company->id,
                CompanyRating::FIELD_COMPANY_LOCATION_ID => $company->locations()->first()->id,
            ],
            ProductCampaign::class                  => [
                ProductCampaign::FIELD_PRODUCT_ID          => Product::query()->first()->id,
                ProductCampaign::FIELD_INDUSTRY_SERVICE_ID => IndustryService::query()->first()->id,
                ProductCampaign::FIELD_COMPANY_ID          => $company->id,
            ],
            CampaignBillingProfile::class           => [
                CampaignBillingProfile::FIELD_BILLING_PROFILE_ID => $company->billingProfiles()->first()->id,
                CampaignBillingProfile::FIELD_CAMPAIGN_ID        => $company->futureCampaigns()->first()->id,
            ],
            default                                 => [
                'company_id' => $company->id,
            ]
        };
    }

    protected function setUp(): void
    {
        parent::setUp();

        Permission::factory()->create([Permission::FIELD_NAME => PermissionType::COMPANY_DELETE->value]);

        $role = Role::findOrCreate(RoleType::ADMIN->value);
        $role->givePermissionTo(PermissionType::COMPANY_DELETE->value);

        $user = User::factory()->create()->assignRole($role);

        ConsumerProduct::factory()->create();
        OpportunityNotificationConfig::factory()->create();
        CustomerSuccessManager::factory()->create([CustomerSuccessManager::FIELD_USER_ID => $user->id]);
        CreditType::factory()->create();

        $this->seed(ProductsSeeder::class);

        $this->actingAs($user);
    }

    public function test_delete_validators_validate(): void
    {
        /** todo: test deletable checks work @see CompanyDeleteService::$validators */
        $this->markTestIncomplete();
    }

    public function test_delete_company(): void
    {
        /** @var Company $companyToBeDeleted */
        $companyToBeDeleted = Company::factory()->createQuietly();
        $companyToBeDeleted->companyIndustries()->delete();
        $companyToBeDeleted->contracts()->delete();
        $companyToBeDeleted->companyServices()->delete();

        /** @var Company $companyToNotBeDeleted */
        $companyToNotBeDeleted = Company::factory()->createQuietly();
        $companyToNotBeDeleted->companyIndustries()->delete();

        $this->createCompanyModels(company: $companyToNotBeDeleted);

        $models = $this->createCompanyModels(company: $companyToBeDeleted);

        $mock = Mockery::mock(CompanyDeleteService::class, [$companyToBeDeleted])->makePartial();

        $mock->shouldAllowMockingProtectedMethods()
            ->shouldReceive('markedForDeletion')
            ->andReturn(true);

        $reflection = new \ReflectionClass($mock);
        $deletable = $reflection->getProperty('deletable');
        $deletable->setAccessible(true);
        $deletable->setValue($mock, true);

        // Step 1: Get the table names of the models you're changing
        $affectedTables = collect($models)->map(fn($model) => $model::TABLE)->unique();

        // Step 2: Get all table names in the database
        $allTables = collect(DB::select('SHOW TABLES'))->map(function ($table) {
            return array_values((array)$table)[0];
        });

        $allTableCount = $allTables->mapWithKeys(fn($table) => [$table => DB::table($table)->count()]);

        foreach ($models as $model) {
            if ($model instanceof Projection) {
                $model->refresh();
            }
            $this->assertDatabaseHas($model::TABLE, ['id' => $model->id]);
        }

        $mock->delete();

        foreach ($allTableCount as $table => $count) {
            // make sure that the company to be deleted only had one record
            if (in_array($table, $affectedTables->toArray())) {
                $this->assertDatabaseCount($table, $count - 1);
            } else {
                $this->assertDatabaseCount($table, $count);
            }
        }
    }

    protected function createCompanyModels(Company $company): array
    {
        $mock = Mockery::mock(CompanyDeleteService::class, [$company])->makePartial();

        $reflection = new \ReflectionClass($mock);
        $property = $reflection->getProperty('deletes');
        $property->setAccessible(true);

        $deletes = $property->getValue($mock);

        $deletes = array_reverse($deletes);

        $models = [];

        foreach ($deletes as $delete) {

            if (is_subclass_of($delete, DeleteContract::class)) {
                /** @var DeleteContract $delete */
                $delete = app($delete);
                if ($delete instanceof DeleteCompany) {
                    $models[] = $company;

                    continue;
                }

                $modelClass = $delete->modelClass();
            } else {
                $modelClass = $delete;
            }

            $models[] = $modelClass::factory()->createQuietly(
                $this->setOtherFields($modelClass, $company)
            );
        }

        return $models;
    }
}
