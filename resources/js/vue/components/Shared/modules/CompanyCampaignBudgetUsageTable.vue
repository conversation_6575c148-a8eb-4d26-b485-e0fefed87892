<template>
    <div>
        <alerts-container :dark-mode="darkMode" v-if="alertActive" :alert-type="alertType" :text="alertText" />

        <div class="grid grid-cols-7 gap-x-3 mb-2 px-5">
            <div class="flex text-slate-400 font-medium items-center">
                <svg v-if="sortBy === 'campaign_name'"
                     :class="{'rotate-180': sortDir === 'asc'}"
                     class="ml-2 transform transition-all duration-200 w-6 fill-current text-blue-550"
                     width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M9.70711 0.292893C10.0976 0.683418 10.0976 1.31658 9.70711 1.70711L5.70711 5.70711C5.51957 5.89464 5.26522 6 5 6C4.73478 6 4.48043 5.89464 4.29289 5.70711L0.292893 1.70711C-0.0976315 1.31658 -0.0976315 0.683417 0.292893 0.292893C0.683417 -0.0976315 1.31658 -0.0976314 1.70711 0.292893L5 3.58579L8.29289 0.292893C8.68342 -0.0976311 9.31658 -0.0976311 9.70711 0.292893Z"/>
                </svg>
                <p class=" tracking-wide uppercase text-xs cursor-pointer" :class="{'text-blue-550 font-bold': sortBy === 'campaign_name'}" @click="handleSort('campaign_name')">Campaign</p>
            </div>
            <p class="text-slate-400 font-medium tracking-wide uppercase text-xs text-center">Status</p>
            <div class="flex justify-center text-slate-400 font-medium items-center">
                <svg v-if="sortBy === 'spend_today'"
                     :class="{'rotate-180': sortDir === 'asc'}"
                     class="ml-2 transform transition-all duration-200 w-6 fill-current text-blue-550"
                     width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M9.70711 0.292893C10.0976 0.683418 10.0976 1.31658 9.70711 1.70711L5.70711 5.70711C5.51957 5.89464 5.26522 6 5 6C4.73478 6 4.48043 5.89464 4.29289 5.70711L0.292893 1.70711C-0.0976315 1.31658 -0.0976315 0.683417 0.292893 0.292893C0.683417 -0.0976315 1.31658 -0.0976314 1.70711 0.292893L5 3.58579L8.29289 0.292893C8.68342 -0.0976311 9.31658 -0.0976311 9.70711 0.292893Z"/>
                </svg>
                <p class="tracking-wide uppercase text-xs text-center cursor-pointer" :class="{'text-blue-550 font-bold': sortBy === 'spent_today'}" @click="handleSort('spend_today')">Spent Today</p>
            </div>
            <div class="flex justify-center text-slate-400 font-medium items-center">
                <svg v-if="sortBy === 'avg_daily_spend'"
                     :class="{'rotate-180': sortDir === 'asc'}"
                     class="ml-2 transform transition-all duration-200 w-6 fill-current text-blue-550"
                     width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M9.70711 0.292893C10.0976 0.683418 10.0976 1.31658 9.70711 1.70711L5.70711 5.70711C5.51957 5.89464 5.26522 6 5 6C4.73478 6 4.48043 5.89464 4.29289 5.70711L0.292893 1.70711C-0.0976315 1.31658 -0.0976315 0.683417 0.292893 0.292893C0.683417 -0.0976315 1.31658 -0.0976314 1.70711 0.292893L5 3.58579L8.29289 0.292893C8.68342 -0.0976311 9.31658 -0.0976311 9.70711 0.292893Z"/>
                </svg>
                <p class="tracking-wide uppercase text-xs text-center cursor-pointer" :class="{'text-blue-550 font-bold': sortBy === 'avg_daily_spend'}"  @click="handleSort('avg_daily_spend')">Avg. Daily Spend</p>
            </div>
            <div class="flex justify-center text-slate-400 font-medium items-center">
                <svg v-if="sortBy === 'daily_budget'"
                     :class="{'rotate-180': sortDir === 'asc'}"
                     class="ml-2 transform transition-all duration-200 w-6 fill-current text-blue-550"
                     width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M9.70711 0.292893C10.0976 0.683418 10.0976 1.31658 9.70711 1.70711L5.70711 5.70711C5.51957 5.89464 5.26522 6 5 6C4.73478 6 4.48043 5.89464 4.29289 5.70711L0.292893 1.70711C-0.0976315 1.31658 -0.0976315 0.683417 0.292893 0.292893C0.683417 -0.0976315 1.31658 -0.0976314 1.70711 0.292893L5 3.58579L8.29289 0.292893C8.68342 -0.0976311 9.31658 -0.0976311 9.70711 0.292893Z"/>
                </svg>
                <p class="tracking-wide uppercase text-xs text-center cursor-pointer" :class="{'text-blue-550 font-bold': sortBy === 'daily_budget'}"  @click="handleSort('daily_budget')">Daily Budget</p>
            </div>
            <div class="flex justify-center text-slate-400 font-medium items-center">
                <svg v-if="sortBy === 'budget_usage'"
                     :class="{'rotate-180': sortDir === 'asc'}"
                     class="ml-2 transform transition-all duration-200 w-6 fill-current text-blue-550"
                     width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M9.70711 0.292893C10.0976 0.683418 10.0976 1.31658 9.70711 1.70711L5.70711 5.70711C5.51957 5.89464 5.26522 6 5 6C4.73478 6 4.48043 5.89464 4.29289 5.70711L0.292893 1.70711C-0.0976315 1.31658 -0.0976315 0.683417 0.292893 0.292893C0.683417 -0.0976315 1.31658 -0.0976314 1.70711 0.292893L5 3.58579L8.29289 0.292893C8.68342 -0.0976311 9.31658 -0.0976311 9.70711 0.292893Z"/>
                </svg>
                <p class="tracking-wide uppercase text-xs text-center cursor-pointer" :class="{'text-blue-550 font-bold': sortBy === 'budget_usage'}" @click="handleSort('budget_usage')">Budget Usage</p>
            </div>
            <div class="flex justify-center text-slate-400 font-medium items-center">
                <svg v-if="sortBy === 'max_budget_usage'"
                     :class="{'rotate-180': sortDir === 'asc'}"
                     class="ml-2 transform transition-all duration-200 w-6 fill-current text-blue-550"
                     width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M9.70711 0.292893C10.0976 0.683418 10.0976 1.31658 9.70711 1.70711L5.70711 5.70711C5.51957 5.89464 5.26522 6 5 6C4.73478 6 4.48043 5.89464 4.29289 5.70711L0.292893 1.70711C-0.0976315 1.31658 -0.0976315 0.683417 0.292893 0.292893C0.683417 -0.0976315 1.31658 -0.0976314 1.70711 0.292893L5 3.58579L8.29289 0.292893C8.68342 -0.0976311 9.31658 -0.0976311 9.70711 0.292893Z"/>
                </svg>
                <p class="tracking-wide uppercase text-xs text-center cursor-pointer" :class="{'text-blue-550 font-bold': sortBy === 'max_budget_usage'}" @click="handleSort('max_budget_usage')">Max Budget Usage</p>
            </div>
        </div>
        <template v-if="!loading && paginationData?.total > 0">
            <div class="border-t border-b overflow-y-auto"
                 :class="[darkMode ? 'bg-dark-background border-dark-border' : 'bg-light-background  border-light-border']">
                <div>
                    <div class="grid grid-cols-7 gap-x-3 border-b px-5 py-3 items-center"
                         v-for="campaignBudget in campaignBudgets" :key="campaignBudget.budget_id"
                         :class="{'text-slate-900 hover:bg-light-module border-light-border': !darkMode, 'text-slate-100 hover:bg-dark-module border-dark-border': darkMode}">
                        <div @click="handleCampaignSelected(campaignBudget)" class="text-sm truncate cursor-pointer text-primary-500">
                            {{  campaignBudget.campaign_name }} ({{campaignBudget.budget_display_name}})
                        </div>
                        <div class="flex flex-col justify-center items-center gap-2">
                            <company-campaign-status-badge
                                :dark-mode="darkMode"
                                :status="campaignBudget.campaign_status"
                            />
                            <p v-if="campaignBudget.paused_until" class="text-xs">{{ campaignBudget.paused_until }}</p>
                            <p v-else-if="campaignBudget.paused_at" class="text-xs">{{ campaignBudget.paused_at }}</p>
                        </div>
                        <p class="text-sm truncate text-center">
                            {{  campaignBudget.spend_today }}
                        </p>
                        <p class="text-sm truncate text-center">
                            {{ campaignBudget.average_daily_spend }}
                        </p>
                        <p class="text-sm truncate text-center">
                            {{ campaignBudget.budget_type}}
                        </p>
                        <p class="text-sm truncate text-center">
                            {{ campaignBudget.budget_usage_today }}
                        </p>
                        <p class="text-sm truncate text-center">
                            {{ campaignBudget.maximum_budget_usage }}
                        </p>
                    </div>
                </div>
            </div>
            <div class="p-3">
                <Pagination :dark-mode="darkMode" :pagination-data="paginationData" :show-pagination="true" @change-page="handlePaginationEvent"></Pagination>
            </div>
        </template>
        <template v-else-if="!loading">
            <div class="text-center w-full my-10">
                No data
            </div>
        </template>
        <template v-else-if="loading">
            <div class="text-center w-full my-10">
                <loading-spinner/>
            </div>
        </template>
        <CampaignWizard
            v-if="showCampaignWizard"
            :dark-mode="darkMode"
            :company-id="companyId"
            :editing-campaign-reference="editingFutureCampaign"
            :future-industry-services="futureIndustryServices"
            :readonly="true"
            @close:wizard="closeCampaignWizard(false)"
            @refresh-and-close:wizard="closeCampaignWizard(true)"
        />
    </div>
</template>

<script>
    import SharedApiService from "../services/api";
    import Pagination from "../components/Pagination.vue";
    import LoadingSpinner from "../components/LoadingSpinner.vue";
    import AlertsContainer from "../components/AlertsContainer.vue";
    import AlertsMixin from "../../../mixins/alerts-mixin";
    import LegacyAdminMixin from "../mixins/legacy-admin-mixin";
    import CompanyCampaignStatusBadge from "../components/CompanyCampaign/CompanyCampaignStatusBadge.vue";
    import CampaignWizard from "../../Campaigns/Wizard/CampaignWizard.vue";
    import {useFutureCampaignStore} from "../../Campaigns/Wizard/stores/future-campaigns.js";


    const SORT_ASC = 'asc';
    const SORT_DESC = 'desc';
    export default {
        name: "CompanyCampaignBudgetUsageTable",
        components: {
            CampaignWizard,
            CompanyCampaignStatusBadge,
            AlertsContainer,
            LoadingSpinner,
            Pagination
        },
        props: {
            darkMode: {
                type: Boolean,
                default: false
            },
            companyId: {
                type: Number,
                default: 0,
                required: true
            },
        },
        mixins: [
            AlertsMixin,
            LegacyAdminMixin
        ],
        created: function() {
            this.api = SharedApiService.make();
            this.getCompanyBudgetUsage();
        },
        data: function() {
            return {
                api: null,
                campaignBudgets: null,
                paginationData: null,
                loading: false,
                alertType: '',
                alertActive: false,
                alertText: '',
                sortBy: null,
                sortDir: null,
                tableFilters: null,
                page: 1,
                perPage: 10,
                showCampaignWizard: false,
                campaignStore: useFutureCampaignStore(),
                futureIndustryServices: [],
                editingFutureCampaign: null,
            };
        },
        methods: {
            async handlePaginationEvent(newPageUrl) {
                this.loading = true;
                this.page = newPageUrl.newPage
                await this.getCompanyBudgetUsage();
            },
            handleSort(column) {
                if (this.sortBy !== column) {
                    this.sortBy = column;
                    this.sortDir = SORT_ASC;
                } else if(this.sortBy === column) {
                    this.sortDir = (this.sortDir === SORT_ASC) ? SORT_DESC : SORT_ASC
                }
                this.page = 1;
                this.tableFilters = { 'sortBy': this.sortBy, 'sortDir': this.sortDir }
                this.getCompanyBudgetUsage();
            },
            async getCompanyBudgetUsage() {
                this.loading = true;
                this.api.getCompanyBudgetUsage(this.companyId, this.page, this.tableFilters).then(res => {
                    if(res.data.data.status === true) {
                        let {data, links, meta} = res.data.data.campaigns;
                        this.campaignBudgets = data;
                        this.paginationData = meta;
                    }
                    else {
                        this.showAlert('error', 'Failed to retrieve campaign budgets');
                    }
                }).catch(() => {
                    this.showAlert('error', 'Failed to retrieve campaign budgets');
                }).finally(() => {
                    this.loading = false;
                });
            },
            closeCampaignWizard(refreshCampaignList = false) {
                if (refreshCampaignList) {
                    this.loading = true;
                    this.campaignStore.getCampaignList(this.getFutureCampaignFilters()).then(() => this.loading = false);
                }

                this.showCampaignWizard = false;
            },
            async getFutureIndustries() {
                this.campaignStore.apiService.getIndustryServiceProductOptions(this.companyId).then(resp => {
                    if (resp.data?.data?.status) {
                        this.futureIndustryServices = resp.data.data.industry_services;
                        this.customFloorPricingIndustryIds = resp.data.data.floor_price_industry_ids ?? [];
                    }
                }).catch(e => {
                    this.showAlert('error', e);
                }).finally(() => {
                    this.loading = false;
                });
            },
            handleCampaignSelected(campaignBudget) {
                this.editingFutureCampaign = campaignBudget.campaign_reference;
                this.showCampaignWizard = true;
            }
        }
    }
</script>

<style scoped>

</style>
