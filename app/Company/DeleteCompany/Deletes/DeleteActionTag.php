<?php

namespace App\Company\DeleteCompany\Deletes;

use App\Models\Action;
use App\Models\ActionTag;
use Illuminate\Database\Eloquent\Builder;

class DeleteActionTag extends DeleteContract
{
    function query(int $companyId): Builder
    {
        return ActionTag::query()
            ->whereHas(ActionTag::RELATION_ACTION, function(Builder $query) use ($companyId) {
                $query->where(Action::FIELD_FOR_RELATION_TYPE, Action::RELATION_TYPE_COMPANY)
                    ->where(Action::FIELD_FOR_ID, $companyId);
            });
    }

    function modelClass(): string
    {
        return ActionTag::class;
    }
}
