<?php

namespace App\Company\DeleteCompany\Deletes;

use App\Models\Billing\CampaignBillingProfile;
use App\Models\Campaigns\CompanyCampaign;
use Illuminate\Database\Eloquent\Builder;

class DeleteCampaignBillingProfile extends DeleteContract
{
    function query(int $companyId): Builder
    {
        return CampaignBillingProfile::query()
            ->whereHas(CampaignBillingProfile::RELATION_CAMPAIGN, function(Builder $query) use ($companyId) {
                $query->where(CompanyCampaign::FIELD_COMPANY_ID, $companyId);
            });
    }

    function modelClass(): string
    {
        return CampaignBillingProfile::class;
    }
}
