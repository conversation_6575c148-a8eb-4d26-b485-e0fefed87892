<?php

namespace App\Models\Billing;

use App\Models\BaseModel;
use App\Models\Campaigns\CompanyCampaign;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class CampaignBillingProfile extends BaseModel
{
    use HasFactory, SoftDeletes;

    protected $table = self::TABLE;

    const string TABLE                    = 'campaign_billing_profile';
    const string FIELD_CAMPAIGN_ID        = 'campaign_id';
    const string FIELD_BILLING_PROFILE_ID = 'billing_profile_id';
    const string FIELD_DELETED_AT         = 'deleted_at';

    const string RELATION_CAMPAIGN        = 'campaign';
    const string RELATION_BILLING_PROFILE = 'billingProfile';

    public function campaign(): BelongsTo
    {
        return $this->belongsTo(CompanyCampaign::class, self::FIELD_CAMPAIGN_ID, CompanyCampaign::FIELD_ID);
    }

    public function billingProfile(): BelongsTo
    {
        return $this->belongsTo(BillingProfile::class, self::FIELD_BILLING_PROFILE_ID, BillingProfile::FIELD_ID);
    }
}
