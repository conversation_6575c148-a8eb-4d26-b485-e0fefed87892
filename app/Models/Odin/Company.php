<?php

namespace App\Models\Odin;

use App\Concerns\QueueSortable;
use App\Contracts\Legacy\Payments\PaymentMethodAggregatorContract;
use App\Enums\ActivityType;
use App\Enums\Billing\BillingVersion;
use App\Enums\Company\CompanyAdminStatus;
use App\Enums\Company\CompanyCampaignStatus;
use App\Enums\Company\CompanySystemStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Enums\CompanyMetrics\CompanyMetricRequestTypes;
use App\Enums\CompanySalesStatus;
use App\Models\AccountManagerClient;
use App\Models\Action;
use App\Models\ActivityFeed;
use App\Models\BaseModel;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\Invoice;
use App\Models\Cadence\CompanyCadenceRoutine;
use App\Models\Call;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCRMTemplate;
use App\Models\CompanyContract;
use App\Models\CompanyMetric;
use App\Models\CompanyNotificationSetting;
use App\Models\CompanyOptInName;
use App\Models\CompanySlug;
use App\Models\CompanyUserRelationship;
use App\Models\ComputedRejectionStatistic;
use App\Models\ContractorProfile\ContractorProfile;
use App\Models\Email;
use App\Models\Legacy\CompanyRanking;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentInvoice;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\License;
use App\Models\Prospects\NewBuyerProspect;
use App\Models\Ruleset;
use App\Models\Sales\Task;
use App\Models\SalesIntel\FailedUserImportRecord;
use App\Models\SalesIntel\UserImportRecord;
use App\Models\ServiceRadius;
use App\Models\SuccessManagerClient;
use App\Models\Territory\RelationshipManager;
use App\Models\Text;
use App\Models\User;
use App\Services\CompanySlugService;
use App\Services\Legacy\Payments\Gateways\PaymentGatewayFactory;
use App\Traits\HasReference;
use App\Traits\HasStatuses;
use App\Traits\HasUserAssignments;
use App\Traits\HasUuid;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Random\RandomException;

/**
 * @property int $id
 * @property string $reference
 * @property string $uuid
 * @property string $name
 * @property string $entity_name
 * @property string $website
 * @property string $website_verified_url
 * @property string|null $sourced_from
 * @property string $link_to_logo
 * @property CompanyConsolidatedStatus|null $consolidated_status
 * @property int $sales_status
 * @property bool $campaigns_are_partially_suspended
 * @property int $status //deprecated
 * @property CompanyAdminStatus $admin_status
 * @property int $legacy_id
 * @property int|null $relationship_manager_id
 * @property string|null $watchdog_id
 * @property boolean $admin_locked //deprecated
 * @property boolean $admin_approved //deprecated
 * @property boolean $archived
 * @property boolean $imported
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @Property Carbon $prescreened_at
 * @property bool $never_exceed_budget
 * @property ?int $active_opt_in_name_id
 * @property string|null $registration_domain
 * @property bool $bypass_contract_signing
 * @property int|null $preassigned_account_manager_user_id
 * @property CompanyCampaignStatus $campaign_status
 * @property CompanySystemStatus $system_status
 * @property int $legacy_lifetime_revenue_cent
 *
 * @property-read CompanyConfiguration $configuration
 * @property-read CompanyData $data
 * @property-read Collection<CompanyUser> $users
 * @property-read Collection<IndustryType> $industryTypes
 * @property-read Collection<Industry> $industries
 * @property-read Collection<IndustryService> $services
 * @property-read Collection<ProductAssignment> $productAssignments
 * @property-read Collection<CompanyReview> $reviews
 * @property-read Collection<CompanyExpertReview> $expertReviews
 * @property-read Collection<CompanyLocation> $locations
 * @property-read EloquentCompany $legacyCompany
 * @property-read bool $prescreened
 * @property-read Collection<CompanyContract> $contracts
 * @property-read Call|null $latestCall
 * @property-read Text|null $latestText
 * @property-read Email|null $latestEmail
 * @property-read Collection<GenericProfitabilityAssumptionConfiguration> $profitabilityAssumptionConfigurations
 * @property-read Collection<EloquentInvoice> $invoices
 * @property-read Collection<CompanyMediaAsset> $companyMediaAssets
 * @property-read Collection<License> $licenses
 * @property-read Collection<CompanyCadenceRoutine> $cadenceRoutines
 * @property-read Collection<CompanyCadenceRoutine> $allCadenceRoutines
 * @property-read array $last_contacted_at
 * @property-read Collection<ActivityFeed> $activityFeeds
 * @method Builder sortByLastTimeContacted(string $direction)
 * @property-read Collection<CompanyCampaign> $futureCampaigns
 * @property-read Collection<ComputedRejectionStatistic> $rejectionStatistics
 * @property-read Collection<CompanySlug> $companySlugs
 * @property-read CompanySlug $activeCompanySlug
 * @property-read Collection<CompanyMetric> $companyMetrics
 * @property-read Collection<CompanyChangeLog> $changeLogs
 * @property-read CompanyRanking|null $companyRanking
 * @property-read Collection<CompanyCRMTemplate> $crmTemplates
 * @property-read ContractorProfile|null $contractorProfile
 * @property-read RelationshipManager $relationshipManager
 * @property-read Collection<BillingProfile> $billingProfiles
 * @property-read ?CompanyOptInName $activeOptInName
 * @property-read Collection<CompanyOptInName> $optInNames
 * @property-read Collection<NewBuyerProspect> $newBuyerProspects
 * @property-read ?NewBuyerProspect $latestProspect
 * @property-read ?User $preassignedAccountManager
 * @property-read ?CompanyLocation $primaryLocation
 * @property-read Collection<Invoice> $invoicesV2
 * @property-read Collection<Task> $tasks
 * @property-read Collection<CompanyUserRelationship> $userRelationships
 * @property-read ?ServiceRadius $serviceRadius
 * @property-read Collection<CompanyNotificationSetting> $companyNotificationSettings
 */
class Company extends BaseModel
{
    use HasReference, HasUuid, HasFactory, SoftDeletes, QueueSortable, HasUserAssignments, HasStatuses;

    const TABLE = 'companies';

    const FIELD_ID                                = 'id';
    const FIELD_UUID                              = 'uuid';
    const FIELD_REFERENCE                         = 'reference';
    const FIELD_NAME                              = 'name';
    const FIELD_ENTITY_NAME                       = 'entity_name';
    const FIELD_WEBSITE                           = 'website';
    const FIELD_WEBSITE_VERIFIED_URL              = 'website_verified_url';
    const FIELD_WEBSITE_VERIFIED_AT               = 'website_verified_at';
    const FIELD_SOURCED_FROM                      = 'sourced_from';
    const FIELD_LINK_TO_LOGO                      = 'link_to_logo';
    const FIELD_CONSOLIDATED_STATUS               = 'consolidated_status';
    const FIELD_SALES_STATUS                      = 'sales_status';
    const FIELD_CAMPAIGNS_ARE_PARTIALLY_SUSPENDED = 'campaigns_are_partially_suspended';

    /** @deprecated  */
    const FIELD_STATUS                            = 'status';

    const FIELD_ADMIN_STATUS                      = 'admin_status';

    /** @deprecated  */
    const FIELD_ADMIN_LOCKED                      = 'admin_locked';

    /** @deprecated  */
    const FIELD_ADMIN_APPROVED                    = 'admin_approved';

    const FIELD_ARCHIVED                          = 'archived';
    const FIELD_IMPORTED                          = 'imported';
    const FIELD_PRESCREENED_AT                    = 'prescreened_at';
    const FIELD_LEGACY_ID                         = 'legacy_id';
    const FIELD_WATCHDOG_ID                       = 'watchdog_id';
    const FIELD_NEVER_EXCEED_BUDGET               = 'never_exceed_budget';
    const FIELD_RELATIONSHIP_MANAGER_ID           = 'relationship_manager_id';
    const FIELD_ACTIVE_OPT_IN_NAME_ID             = 'active_opt_in_name_id';
    const FIELD_REGISTRATION_DOMAIN               = 'registration_domain';

    // flag for large companies to bypass signing the contract in dashboard
    const string FIELD_BYPASS_CONTRACT_SIGNING    = 'bypass_contract_signing';

    const string FIELD_PREASSIGNED_ACCOUNT_MANAGER_USER_ID = 'preassigned_account_manager_user_id';
    const string FIELD_CAMPAIGN_STATUS = 'campaign_status';
    const string FIELD_SYSTEM_STATUS = 'system_status';
    const string FIELD_LEGACY_LIFETIME_REVENUE_CENT        = 'legacy_lifetime_revenue_cent';

    const STATUS_MERGED                    = -2;
    const STATUS_SYNC_ERROR                = -1;
    const STATUS_LEADS_ACTIVE              = 1;
    const STATUS_LEADS_PAUSED              = 2;
    const STATUS_LEADS_OFF                 = 3;
    const STATUS_LEADS_OFF_NEVER_PURCHASED = 4;
    const STATUS_REGISTERING               = 5;
    const STATUS_PENDING_APPROVAL          = 6;
    const STATUS_PROFILE_ONLY              = 7;

    const ADMIN_STATUS_MERGED         = -2;
    const ADMIN_STATUS_PART_SUSPENDED = 1;
    const ADMIN_STATUS_ALL_SUSPENDED  = 2;
    const ADMIN_STATUS_COLLECTIONS    = 3;
    const ADMIN_STATUS_ARCHIVED       = 4;
    const ADMIN_STATUS_ADMIN_LOCKED   = 5;

    const PRESCREENED_MAX_DAYS = 365;

    const RELATION_CONFIGURATION                    = 'configuration';
    const RELATION_DATA                             = 'data';
    const RELATION_USERS                            = 'users';
    const RELATION_INDUSTRY_TYPES                   = 'industryTypes';
    const RELATION_INDUSTRIES                       = 'industries';
    const RELATION_SERVICES                         = 'services';
    const RELATION_PRODUCT_ASSIGNMENTS              = 'productAssignments';
    const RELATION_REVIEWS                          = 'reviews';
    const RELATION_INVOICES                         = 'invoices';
    const RELATION_LOCATIONS                        = 'locations';
    const RELATION_CAMPAIGNS                        = 'campaigns';
    const RELATION_PRODUCT_CAMPAIGNS                = 'productCampaigns';
    const RELATION_CONTRACTS                        = 'contracts';
    const RELATION_LEGACY_COMPANY                   = 'legacyCompany';
    const RELATION_LEGACY_QUOTE_COMPANIES           = 'legacyQuoteCompanies';
    const RELATION_PROFITABILITY_CONFIGURATION      = 'profitabilityAssumptionConfigurations';
    const RELATION_CHANGE_LOGS                      = 'changeLogs';
    const RELATION_LICENSES                         = 'licenses';
    const RELATION_ACCOUNT_MANAGER_CLIENTS          = 'accountManagerClients';
    const RELATION_SUCCESS_MANAGER_CLIENTS          = 'successManagerClients';
    const RELATION_CADENCE_ROUTINES                 = 'cadenceRoutines';
    const RELATION_ACTIVITY_FEEDS                   = 'activityFeeds';
    const RELATION_FUTURE_CAMPAIGNS                 = 'futureCampaigns';
    const RELATION_REJECTION_STATISTICS             = 'rejectionStatistics';
    const RELATION_ALL_CADENCE_ROUTINES             = 'allCadenceRoutines';
    const RELATION_MEDIA_ASSETS                     = 'companyMediaAssets';
    const RELATION_COMPANY_LINKS                    = 'companyLinks';
    const RELATION_COMPANY_SLUGS                    = 'companySlugs';
    const RELATION_ACTIVE_COMPANY_SLUG              = 'activeCompanySlug';
    const RELATION_COMPANY_METRICS                  = 'companyMetrics';
    const RELATION_COMPANY_RANKING                  = 'companyRanking';
    const RELATION_RELATIONSHIP_MANAGER             = 'relationshipManager';
    const RELATION_ALL_CUSTOMER_SUCCESS_MANAGERS    = 'allCustomerSuccessManagers';
    const RELATION_ACTIVE_CUSTOMER_SUCCESS_MANAGERS = 'activeCustomerSuccessManagers';
    const RELATION_CONTRACTOR_PROFILE               = 'contractorProfile';
    const RELATION_BILLING_PROFILES                 = 'billingProfiles';
    const RELATION_NEW_BUYER_PROSPECTS              = 'newBuyerProspects';
    const RELATION_LATEST_PROSPECT                  = 'latestProspect';
    const RELATION_PRIMARY_LOCATION                 = 'primaryLocation';
    const string RELATION_INVOICES_V2               = 'invoicesV2';
    const RELATION_TASKS                            = 'tasks';
    const RELATION_ACCOUNT_MANAGER                  = 'accountManager';
    const RELATION_BUSINESS_DEVELOPMENT_MANAGER     = 'businessDevelopmentManager';

    /** @deprecated  */
    const STATUSES = [
        self::STATUS_MERGED,
        self::STATUS_SYNC_ERROR,
        self::STATUS_LEADS_ACTIVE,
        self::STATUS_LEADS_PAUSED,
        self::STATUS_LEADS_OFF,
        self::STATUS_LEADS_OFF_NEVER_PURCHASED,
        self::STATUS_REGISTERING,
        self::STATUS_PENDING_APPROVAL,
        self::STATUS_PROFILE_ONLY,
    ];

    /** @deprecated  */
    const STATUSES_STRING = [
        self::STATUS_MERGED                    => "Merged into another Company",
        self::STATUS_SYNC_ERROR                => "Sync Error",
        self::STATUS_LEADS_ACTIVE              => "Leads Active",
        self::STATUS_LEADS_PAUSED              => "Leads Paused",
        self::STATUS_LEADS_OFF                 => "Leads Off",
        self::STATUS_LEADS_OFF_NEVER_PURCHASED => "Leads Off (Never Purchased)",
        self::STATUS_REGISTERING               => "Registering",
        self::STATUS_PENDING_APPROVAL          => "Pending Approval",
        self::STATUS_PROFILE_ONLY              => "Profile Only",
    ];

    /** @deprecated  */
    const ADMIN_STATUSES = [
        self::STATUS_MERGED,
        self::STATUS_SYNC_ERROR,
        self::STATUS_LEADS_ACTIVE,
        self::STATUS_LEADS_PAUSED,
        self::STATUS_LEADS_OFF,
        self::STATUS_LEADS_OFF_NEVER_PURCHASED,
        self::STATUS_REGISTERING,
        self::STATUS_PENDING_APPROVAL,
        self::STATUS_PROFILE_ONLY,
    ];

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_CONSOLIDATED_STATUS => CompanyConsolidatedStatus::class,
        self::FIELD_SALES_STATUS        => CompanySalesStatus::class,
        self::FIELD_PRESCREENED_AT      => 'datetime',
        self::FIELD_CAMPAIGN_STATUS     => CompanyCampaignStatus::class,
        self::FIELD_SYSTEM_STATUS       => CompanySystemStatus::class,
        self::FIELD_ADMIN_STATUS        => CompanyAdminStatus::class
    ];

    /**
     * @param string $reference
     * @return Company|null
     */
    public static function findByReference(string $reference): ?Company
    {
        /** @var ?Company $company */
        $company = Company::query()->where(self::FIELD_REFERENCE, $reference)->first();

        return $company;
    }

    /**
     * @return HasOne
     */
    public function configuration(): HasOne
    {
        return $this->hasOne(CompanyConfiguration::class, CompanyConfiguration::FIELD_COMPANY_ID, self::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function data(): HasOne
    {
        return $this->hasOne(CompanyData::class, CompanyData::FIELD_COMPANY_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function users(): HasMany
    {
        return $this->hasMany(CompanyUser::class, CompanyUser::FIELD_COMPANY_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function productAssignments(): HasMany
    {
        return $this->hasMany(ProductAssignment::class, ProductAssignment::FIELD_COMPANY_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(CompanyReview::class, CompanyReview::FIELD_COMPANY_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function expertReviews(): HasMany
    {
        return $this->hasMany(CompanyExpertReview::class, CompanyExpertReview::FIELD_COMPANY_ID, self::FIELD_ID);
    }

    /**
     * @return BelongsToMany
     */
    public function services(): BelongsToMany
    {
        return $this->belongsToMany(IndustryService::class, CompanyService::TABLE, CompanyService::FIELD_COMPANY_ID, CompanyService::FIELD_INDUSTRY_SERVICE_ID);
    }

    public function companyServices(): HasMany
    {
        return $this->hasMany(CompanyService::class, CompanyService::FIELD_COMPANY_ID, self::FIELD_ID);
    }


    /**
     * @return HasMany
     */
    public function locations(): HasMany
    {
        return $this->hasMany(CompanyLocation::class, CompanyLocation::FIELD_COMPANY_ID, self::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function primaryLocation(): HasOne
    {
        return $this->hasOne(CompanyLocation::class, CompanyLocation::FIELD_COMPANY_ID, self::FIELD_ID)->orderByDesc(CompanyLocation::FIELD_IS_PRIMARY);
    }

    // todo: industryTypes

    /**
     * @return BelongsToMany
     */
    public function industries(): BelongsToMany
    {
        return $this->belongsToMany(Industry::class, CompanyIndustry::TABLE);
    }

    public function companyIndustries(): HasMany
    {
        return $this->hasMany(CompanyIndustry::class, CompanyIndustry::FIELD_COMPANY_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function accountManagerClients(): HasMany
    {
        return $this->hasMany(AccountManagerClient::class, AccountManagerClient::FIELD_COMPANY_REFERENCE, self::FIELD_REFERENCE);
    }

    /**
     * @return HasMany
     */
    public function successManagerClients(): HasMany
    {
        return $this->hasMany(SuccessManagerClient::class, SuccessManagerClient::FIELD_COMPANY_REFERENCE, self::FIELD_REFERENCE);
    }

    /**
     * @return Builder|Model|null
     */
    public function accountManagerClient(): Builder|Model|null
    {
        /** @var AccountManagerClient|null $accountManagerClient */
        return AccountManagerClient::query()
            ->where(AccountManagerClient::FIELD_COMPANY_REFERENCE, $this->reference)
            ->where(AccountManagerClient::FIELD_STATUS, AccountManagerClient::STATUS_ACTIVE)
            ->first();
    }

    /**
     * @return Builder|Model|null
     */
    public function successManagerClient(): Builder|Model|null
    {
        /** @var SuccessManagerClient|null $SuccessManagerClient */
        return SuccessManagerClient::query()
            ->where(SuccessManagerClient::FIELD_COMPANY_REFERENCE, $this->reference)
            ->where(SuccessManagerClient::FIELD_STATUS, SuccessManagerClient::STATUS_ACTIVE)
            ->first();
    }

    /**
     * @return BelongsToMany
     */
    public function allCustomerSuccessManagers(): BelongsToMany
    {
        return $this->belongsToMany(
            CustomerSuccessManager::class,
            CustomerManagerCompany::TABLE,
            CustomerManagerCompany::FIELD_COMPANY_ID,
            CustomerManagerCompany::FIELD_CUSTOMER_SUCCESS_MANAGER_ID
        );
    }

    /**
     * @return Collection
     */
    public function servicesNames(): Collection
    {
        return $this->services
            ->pluck(IndustryService::FIELD_NAME)
            ->unique()
            ->values();
    }

    /**
     * @return Builder
     */
    public function actions(?string $search = null, array $categories = []): Builder
    {
        $userIds = CompanyUser::query()
            ->where(CompanyUser::FIELD_COMPANY_ID, $this->{self::FIELD_ID})
            ->pluck(CompanyUser::FIELD_ID)
            ->toArray();

        $query = Action::query()
            ->with([
                Action::RELATION_TYPE_CATEGORY,
                Action::RELATION_TAGS,
                Action::RELATION_FROM_USER,
            ])
            ->where(function (Builder $query) use ($userIds) {
                $query->where(function ($query) {
                    $query
                        ->where(Action::FIELD_FOR_RELATION_TYPE, Action::RELATION_TYPE_COMPANY)
                        ->where(Action::FIELD_FOR_ID, $this->{self::FIELD_ID});
                })
                ->orWhere(function ($query) use ($userIds) {
                    $query
                        ->whereIn(Action::FIELD_FOR_RELATION_TYPE, [Action::RELATION_TYPE_COMPANY_CONTACT, Action::RELATION_TYPE_COMPANY_USER])
                        ->whereIn(Action::FIELD_FOR_ID, $userIds);
                });
            })
            ->orderBy(Action::FIELD_PINNED, 'desc')
            ->latest();

        if ($search) {
            $query->where(function ($query) use ($search) {
                $query
                    ->where(Action::FIELD_SUBJECT, 'like', "%$search%")
                    ->orWhere(Action::FIELD_MESSAGE, 'like', "%$search%");
            });
        }

        if ($categories) {
            $query->whereIn(Action::FIELD_CATEGORY_ID, $categories);
        }

        return $query;
    }

    /**
     * @return BelongsTo
     */
    public function legacyCompany(): BelongsTo
    {
        return $this->belongsTo(EloquentCompany::class, self::FIELD_LEGACY_ID, EloquentCompany::ID);
    }

    /**
     * @return BelongsToMany
     */
    public function companyLinks(): BelongsToMany
    {
        return $this->belongsToMany(Company::class, CompanyLink::TABLE, CompanyLink::FIELD_COMPANY_ID_ONE, CompanyLink::FIELD_COMPANY_ID_TWO)
            ->withPivot(CompanyLink::FIELD_COMMENT)
            ->withTimestamps();
    }

    /**
     * @return HasMany
     */
    public function legacyQuoteCompanies(): HasMany
    {
        return $this->hasMany(EloquentQuoteCompany::class, EloquentQuoteCompany::COMPANY_ID, self::FIELD_LEGACY_ID);
    }

    /**
     * @return HasMany
     */
    public function campaigns(): HasMany
    {
        return $this->hasMany(LeadCampaign::class, LeadCampaign::COMPANY_ID, self::FIELD_LEGACY_ID);
    }

    /**
     * @return HasMany
     */
    public function productCampaigns(): HasMany
    {
        return $this->hasMany(ProductCampaign::class, ProductCampaign::FIELD_COMPANY_ID, self::FIELD_ID);
    }

    /**
     * Get EloquentInvoices by legacy_id
     * @deprecated Legacy Invoices not used for most companies anymore
     * @see invoicesV2
     *
     * @return HasMany
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(EloquentInvoice::class, EloquentInvoice::COMPANY_ID, self::FIELD_LEGACY_ID);
    }

    /**
     * @return HasMany
     */
    public function invoicesV2(): HasMany
    {
        return $this->hasMany(Invoice::class, Invoice::FIELD_COMPANY_ID, self::FIELD_ID);
    }

    /**
     * @return bool
     */
    public function prescreened(): bool
    {
        return $this->{Company::FIELD_PRESCREENED_AT} && (Carbon::now()->diffInDays($this->{Company::FIELD_PRESCREENED_AT}, true) < self::PRESCREENED_MAX_DAYS);
    }

    /**
     * @return HasMany
     */
    public function contracts(): HasMany
    {
        return $this->hasMany(CompanyContract::class, CompanyContract::FIELD_COMPANY_ID, self::FIELD_ID);
    }

    /**
     * @return Attribute
     */
    public function consolidatedStatusObject(): Attribute
    {
        $result = [
            'id'   => $this->consolidated_status,
            'name' => CompanyConsolidatedStatus::label($this->consolidated_status)
        ];

        return Attribute::make(get: fn() => $result);
    }

    /**
     * @return Attribute
     */
    public function campaignsArePartiallySuspended(): Attribute
    {
        return Attribute::make(get: fn() => $this->determineCampaignsArePartiallySuspended());
    }

    /**
     * @return bool
     * @todo As of now, this should always return false. This is a placeholder for future functionality. Until payment methods are linked to campaigns instead of companies, this value cannot be properly evaluated.
     */
    public function determineCampaignsArePartiallySuspended(): bool
    {
        return false;
    }

    /**
     * @return Collection|Call|null
     */
    public function latestCall(): Collection|Call|null
    {
        return $this->getLatestActivityFeedWithType([ActivityType::CALL->value])?->item;
    }

    /**
     * @return Collection|Text|null
     */
    public function latestText(): Collection|Text|null
    {
        return $this->getLatestActivityFeedWithType([ActivityType::TEXT->value])?->item;
    }

    /**
     * @return Collection|Email|null
     */
    public function latestEmail(): Collection|Email|null
    {
        return $this->getLatestActivityFeedWithType([ActivityType::EMAIL->value])?->item;
    }

    /**
     * @return HasMany
     */
    public function profitabilityAssumptionConfigurations(): HasMany
    {
        return $this->hasMany(GenericProfitabilityAssumptionConfiguration::class, GenericProfitabilityAssumptionConfiguration::FIELD_COMPANY_ID, self::FIELD_ID);
    }

    /**
     * Returns the payment gateways for a given company.
     *
     * @return PaymentGatewayFactory|null
     */
    public function paymentGateways(): ?PaymentGatewayFactory
    {
        return $this->legacyCompany?->paymentGateways();
    }

    /**
     * Returns the payment methods for a given company.
     *
     * @return PaymentMethodAggregatorContract|null
     */
    public function paymentMethods(): ?PaymentMethodAggregatorContract
    {
        return $this->legacyCompany?->paymentMethods();
    }

    /**
     * @return HasMany
     */
    public function companyMediaAssets(): HasMany
    {
        return $this->hasMany(CompanyMediaAsset::class, CompanyMediaAsset::FIELD_COMPANY_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function licenses(): HasMany
    {
        return $this->hasMany(License::class, License::FIELD_COMPANY_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function activityFeeds(): HasMany
    {
        return $this->hasMany(ActivityFeed::class, ActivityFeed::FIELD_COMPANY_ID, self::FIELD_ID);
    }

    /**
     * todo to get the latest we need to order by the activity feed type (eg calls and texts)
     *
     * @param array $typeArray
     * @return ActivityFeed|null
     */
    protected function getLatestActivityFeedWithType(array $typeArray): ActivityFeed|null
    {
        return $this->activityFeeds
            ->whereIn(ActivityFeed::FIELD_ITEM_TYPE, $typeArray)
            ->sortByDesc(ActivityFeed::UPDATED_AT)
            ->first();
    }

    /**
     * @return hasMany
     */
    public function scores(): hasMany
    {
        return $this
            ->hasMany(RulesetScore::class, RulesetScore::FIELD_MODEL_ID, self::FIELD_ID)
            ->whereHas(RulesetScore::RELATION_RULESET, function (Builder $query) {
                return $query->whereNull(Ruleset::FIELD_DELETED_AT);
            })
            ->where(RulesetScore::FIELD_MODEL_TYPE, '=', self::TABLE);
    }

    /**
     * @return HasMany
     */
    public function cadenceRoutines(): HasMany
    {
        return $this->hasMany(CompanyCadenceRoutine::class, CompanyCadenceRoutine::FIELD_COMPANY_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function allCadenceRoutines(): HasMany
    {
        return $this->hasMany(CompanyCadenceRoutine::class, CompanyCadenceRoutine::FIELD_COMPANY_ID, self::FIELD_ID)->withTrashed();
    }

    /**
     * @return HasMany
     */
    public function changeLogs(): HasMany
    {
        return $this->hasMany(CompanyChangeLog::class, CompanyChangeLog::FIELD_COMPANY_ID, self::FIELD_ID);
    }

    /**
     * @param Collection $activityFeeds
     * @return array
     */
    public function getLastContactedAt(Collection $activityFeeds): array
    {
        $typeArray = [
            ActivityType::CALL->value,
            ActivityType::TEXT->value,
            ActivityType::EMAIL->value,
        ];

        /**
         * @var ActivityFeed $activityFeed
         */
        $activityFeed = $activityFeeds
            ->where(ActivityFeed::FIELD_COMPANY_ID, $this->{self::FIELD_ID})
            ->whereIn(ActivityFeed::FIELD_ITEM_TYPE, $typeArray)
            ->sortByDesc(ActivityFeed::UPDATED_AT)
            ->first();

        $contactType = $activityFeed?->item;

        if (!$contactType) {
            return [[]];
        }

        if ($contactType instanceof Call) {
            $lastContactedAt = $contactType->call_end;
            $direction       = $contactType->direction;
        } elseif ($contactType instanceof Text) {
            $lastContactedAt = $contactType->{Model::CREATED_AT};
            $direction       = $contactType->direction;
        } else {
            $lastContactedAt = $contactType->{Model::CREATED_AT};
            $direction       = 'outbound';
        }

        return [
            'last_contacted_at_type'      => class_basename($contactType),
            'last_contacted_at_date'      => $lastContactedAt?->format("F j Y g:i A"),
            'last_contacted_at_direction' => ucfirst($direction),
        ];
    }

    /**
     * @return Attribute
     * @property array $last_contacted_at
     */
    public function lastContactedAt(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->getLastContactedAt($this->activityFeeds)
        );
    }


    /**
     * @return Builder
     */
    public function companyUserActions(): Builder
    {
        return Action::query()
            ->where(function (Builder $builder) {
                $builder->where(Action::FIELD_FOR_RELATION_TYPE, Action::RELATION_TYPE_COMPANY_CONTACT)
                    ->orWhere(Action::FIELD_FOR_RELATION_TYPE, Action::RELATION_TYPE_COMPANY_USER);
            })
            ->orderByDesc(Action::FIELD_PINNED)
            ->latest();
    }

    /**
     * @param Builder $query
     * @param string $direction
     * @return void
     */
    public function scopeSortByLastTimeContacted(Builder $query, string $direction): void
    {
        $getQuery = function ($query) {
            /**
             * @var Builder $query
             */
            $query->select('call_end')
                ->from('calls')
                ->join('company_users', function (JoinClause $joinClause) {
                    $joinClause->on('calls.formatted_other_number', '=', 'company_users.formatted_cell_phone')
                        ->orOn('calls.formatted_other_number', '=', 'company_users.formatted_office_phone');
                })
                ->whereColumn('company_users.company_id', '=', 'companies.id')
                ->orderByDesc('call_end')
                ->limit(1);
        };

        if ($direction === 'asc') {
            $query->orderBy(function ($query) use ($getQuery) {
                $getQuery($query);
            });
        } else {
            $query->orderBy(function ($query) use ($getQuery) {
                $getQuery($query);
            }, 'desc');
        }
    }

    /**
     * Defines the relationship between this company and their campaigns.
     *
     * @TODO: Rename this once refactoring is completed.
     *
     * @return HasMany
     */
    public function futureCampaigns(): HasMany
    {
        return $this->hasMany(CompanyCampaign::class, CompanyCampaign::FIELD_COMPANY_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function rejectionStatistics(): HasMany
    {
        return $this->hasMany(ComputedRejectionStatistic::class);
    }

    /**
     * @return HasMany
     */
    public function companySlugs(): HasMany
    {
        return $this->hasMany(CompanySlug::class, CompanySlug::FIELD_COMPANY_ID, self::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function activeCompanySlug(): HasOne
    {
        return $this->hasOne(CompanySlug::class, CompanySlug::FIELD_COMPANY_ID, self::FIELD_ID)
            ->whereNull(CompanySlug::FIELD_REDIRECT_COMPANY_SLUG_ID);
    }

    /**
     * @return void
     * @throws RandomException
     */
    public function formatSlugs(): void
    {
        $companySlugService = app(CompanySlugService::class);
        $companySlugService->formatSlugsForCompany($this);
    }

    /**
     * @return HasMany
     */
    public function companyMetrics(): HasMany
    {
        return $this->hasMany(CompanyMetric::class, CompanyMetric::FIELD_COMPANY_ID);
    }

    /**
     * @return ?CompanyMetric
     */
    public function latestCompanyPPCSpendMetrics(): ?CompanyMetric
    {
        /* @var ?CompanyMetric */
        return $this->companyMetrics()->where(CompanyMetric::FIELD_REQUEST_TYPE, '=', CompanyMetricRequestTypes::PPC_SPEND)->latest(CompanyMetric::FIELD_ID)->first();
    }

    /**
     * @return HasOne
     */
    public function companyRanking(): HasOne
    {
        return $this->hasOne(CompanyRanking::class, CompanyRanking::COMPANY_ID, self::FIELD_LEGACY_ID);
    }

    /**
     * @return HasMany
     */
    public function billingProfiles(): HasMany
    {
        return $this->hasMany(BillingProfile::class, BillingProfile::FIELD_COMPANY_ID);
    }

    /**
     * @return BelongsTo
     */
    public function relationshipManager(): BelongsTo
    {
        return $this->belongsTo(RelationshipManager::class, self::FIELD_RELATIONSHIP_MANAGER_ID, RelationshipManager::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function crmTemplates(): HasMany
    {
        return $this->hasMany(CompanyCRMTemplate::class, CompanyCRMTemplate::FIELD_COMPANY_ID, self::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function contractorProfile(): HasOne
    {
        return $this->hasOne(ContractorProfile::class);
    }

    /**
     * @return string
     */
    public function getBillingVersion(): string
    {
        return $this->{Company::RELATION_LEGACY_COMPANY}?->{EloquentCompany::BILLING_VERSION} ?? BillingVersion::V2->value;
    }

    /**
     * @return HasOne
     */
    public function activeOptInName(): HasOne
    {
        return $this->hasOne(CompanyOptInName::class, CompanyOptInName::FIELD_ID, self::FIELD_ACTIVE_OPT_IN_NAME_ID);
    }

    /**
     * @return HasMany
     */
    public function optInNames(): HasMany
    {
        return $this->hasMany(CompanyOptInName::class);
    }

    /**
     * @return string
     */
    public function getAdminProfileUrl(): string
    {
        return config('app.url') . "/companies/" . $this->id;
    }

    /**
     * @return HasMany
     */
    public function newBuyerProspects(): HasMany
    {
        return $this->hasMany(NewBuyerProspect::class, NewBuyerProspect::FIELD_COMPANY_ID, self::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function latestProspect(): HasOne
    {
        return $this->newBuyerProspects()->one()->latest(NewBuyerProspect::FIELD_ID);
    }

    public function userImportRecords()
    {
        return $this->hasMany(UserImportRecord::class);
    }

    public function failedImportRecords()
    {
        return $this->hasMany(FailedUserImportRecord::class);
    }

    /**
     * @return BelongsTo
     */
    public function preassignedAccountManager(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_PREASSIGNED_ACCOUNT_MANAGER_USER_ID);
    }

    /**
     * @return HasMany
     * @see $tasks
     */
    public function tasks(): HasMany
    {
        return $this->hasMany(Task::class, Task::VIRTUAL_FIELD_MANUAL_COMPANY_ID);
    }

    /**
     * @return HasMany
     */
    public function userRelationships(): HasMany
    {
        return $this->hasMany(CompanyUserRelationship::class);
    }

    /**
     * @return HasOne
     */
    public function serviceRadius(): HasOne
    {
        return $this->hasOne(ServiceRadius::class, ServiceRadius::FIELD_COMPANY_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function companyNotificationSettings(): HasMany
    {
        return $this->hasMany(CompanyNotificationSetting::class);
    }
}
