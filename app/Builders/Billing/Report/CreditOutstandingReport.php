<?php

namespace App\Builders\Billing\Report;

use App\Builders\Billing\BillingBuilder;
use App\Models\Billing\Credit;
use App\Models\Billing\InvoiceCredit;
use App\Models\Odin\Company;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use \Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class CreditOutstandingReport extends BillingBuilder
{
    /**
     * @var array|string[]
     */
    protected array $sortColumnsMap = [
        'company_id'      => Credit::FIELD_COMPANY_ID,
        'remaining_value' => Credit::FIELD_REMAINING_VALUE,
        'applied_at'      => Credit::FIELD_CREATED_AT,
        'expires_at'      => Credit::FIELD_EXPIRES_AT,
        'initial_value'   => Credit::FIELD_INITIAL_VALUE,
        'outstanding'     => 'outstanding',
    ];

    /**
     * @param Builder|QueryBuilder $builder
     */
    public function __construct(protected Builder|QueryBuilder $builder)
    {
        parent::__construct($builder);
    }


    /**
     * @param string|null $creditType
     * @return $this
     */
    public function forCreditType(?string $creditType = null): self
    {
        if (filled($creditType)) {
            $this->builder->where(Credit::FIELD_CREDIT_TYPE, $creditType);
        }

        return $this;
    }

    /**
     * @param string|null $status
     * @return $this
     */
    public function filterByStatus(?string $status = null): self
    {
        if ($status === 'expired_only') {
            $this->builder->where(Credit::TABLE . '.' . Credit::FIELD_EXPIRES_AT, '<', now());
        } else if ($status === 'exclude_expired') {
            $this->builder->where(function ($query) {
                $query->where(Credit::TABLE . '.' . Credit::FIELD_EXPIRES_AT, '>=', now())
                    ->orWhereNull(Credit::TABLE . '.' . Credit::FIELD_EXPIRES_AT);
            });
        }

        return $this;
    }

    /**
     * @param string|null $date
     * @return $this
     */
    public function filterByAppliedToInvoiceAt(?string $date = null): self
    {
        $date = Carbon::parse($date ?? now());

        $this->builder->where(function ($query) use ($date) {
            $query->where(InvoiceCredit::TABLE . '.' . InvoiceCredit::FIELD_APPLIED_AT, '<', $date)
                ->orWhereNull(InvoiceCredit::TABLE . '.' . InvoiceCredit::FIELD_APPLIED_AT);
        });

        return $this;
    }

    /**
     * @param array $filters
     * @return $this
     */
    public function applyFilters(array $filters): self
    {
        $this->safeJoin('company', function () {
            $this->builder->join(
                Company::TABLE,
                Company::TABLE . '.' . Company::FIELD_ID,
                '=',
                Credit::TABLE . '.' . Credit::FIELD_COMPANY_ID
            )->addSelect(Company::TABLE . '.' . Company::FIELD_NAME . ' as company_name');
        });

        $this->forCompanyId(Arr::get($filters, 'company_id'));
        $this->forCreditType(Arr::get($filters, 'credit_type'));
        $this->filterByRole(Arr::get($filters, 'filter_by_role'));
        $this->filterByStatus(Arr::get($filters, 'status'));
        $this->filterByAppliedToInvoiceAt(Arr::get($filters, 'date'));

        return $this;
    }

    /**
     * @return BillingBuilder
     */
    public static function query(): BillingBuilder
    {
        $base = Credit::query()
            ->select([
                Credit::TABLE . '.' . Credit::FIELD_COMPANY_ID,
                Credit::TABLE . '.' . Credit::FIELD_ID,
                Credit::TABLE . '.' . Credit::FIELD_CREATED_AT,
                Credit::TABLE . '.' . Credit::FIELD_EXPIRES_AT,
                Credit::TABLE . '.' . Credit::FIELD_CREDIT_TYPE,
                Credit::TABLE . '.' . Credit::FIELD_REMAINING_VALUE,
                Credit::TABLE . '.' . Credit::FIELD_INITIAL_VALUE,
                InvoiceCredit::TABLE . '.' . InvoiceCredit::FIELD_APPLIED_AT,
                DB::raw(Credit::TABLE . '.' . Credit::FIELD_INITIAL_VALUE . ' - SUM(COALESCE(' . InvoiceCredit::TABLE . '.' . InvoiceCredit::FIELD_AMOUNT_APPLIED . ', 0)) AS outstanding')
            ])
            ->leftJoin(
                InvoiceCredit::TABLE,
                InvoiceCredit::TABLE . '.' . InvoiceCredit::FIELD_CREDIT_ID,
                Credit::TABLE . '.' . Credit::FIELD_ID
            )
            ->with(Credit::RELATION_BILLING_PROFILES)
            ->groupBy(Credit::TABLE . '.' . Credit::FIELD_ID)
            ->having('outstanding', '>', 0);

        return new self($base);
    }
}
