<?php

namespace App\Http\Controllers\Billing;

use App\DTO\Billing\Credit\CreditTypePayload;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Billing\Credit\AddCreditRequest;
use App\Http\Requests\Billing\Credit\CreateCreditTypeRequest;
use App\Http\Requests\Billing\Credit\DeductCreditRequest;
use App\Http\Requests\Billing\Credit\ExpireCreditRequest;
use App\Http\Requests\Billing\Credit\ExtendCreditRequest;
use App\Http\Requests\Billing\Credit\GetCreditTypesRequest;
use App\Http\Requests\Billing\Credit\UpdateCreditTypesRequest;
use App\Http\Resources\Billing\CompanyCreditBalancesResource;
use App\Http\Resources\Billing\CreditEventResource;
use App\Http\Resources\CreditTypes\CreditTypeListResource;
use App\Http\Resources\CreditTypes\CreditTypesResource;
use App\Models\Odin\Company;
use App\Models\User;
use App\Services\Billing\CreditService;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class CreditController extends APIController
{
    const string FIELD_STATUS       = 'status';
    const string FIELD_MESSAGE      = 'message';
    const string FIELD_CREDITS      = 'credits';
    const string FIELD_CREDIT_TYPES = 'credit_types';
    const string FIELD_BALANCES     = 'balances';
    const string FIELD_DATA         = 'data';

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected CreditService $service,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param AddCreditRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function addCredit(AddCreditRequest $request): JsonResponse
    {
        $validated = $request->validated();

        /** @var Company $company */
        $company = Company::query()->findOrFail(Arr::get($validated, AddCreditRequest::FIELD_COMPANY_ID));

        /** @var User $user */
        $user = auth()->user();

        $this->service->requestAddCredit(
            uuid            : Str::uuid()->toString(),
            companyReference: $company->reference,
            companyId       : $company->id,
            amount          : Arr::get($validated, AddCreditRequest::FIELD_VALUE) * 100,
            type            : Arr::get($validated, AddCreditRequest::FIELD_TYPE),
            authorType      : InvoiceEventAuthorTypes::USER->value,
            authorId        : $user->{User::FIELD_ID},
            note            : Arr::get($validated, AddCreditRequest::FIELD_NOTE),
            billingProfileIds: Arr::get($validated, AddCreditRequest::FIELD_BILLING_PROFILE_IDS, []),
        );

        return $this->formatResponse([
            self::FIELD_STATUS  => true,
            self::FIELD_MESSAGE => "Credit allocation requested and pending approval",
            self::FIELD_DATA    => new CreditTypeListResource($this->service->getCompanyCredits($company->{Company::FIELD_ID}))
        ]);
    }

    /**
     * @param DeductCreditRequest $request
     * @return JsonResponse
     */
    public function deductCredit(DeductCreditRequest $request): JsonResponse
    {
        $validated = $request->validated();

        /** @var Company $company */
        $company = Company::query()->findOrFail(Arr::get($validated, DeductCreditRequest::FIELD_COMPANY_ID));

        /** @var User $user */
        $user = auth()->user();

        $this->service->deductCredit($company, $user, $validated);

        return $this->formatResponse([
            self::FIELD_STATUS => true,
        ]);
    }

    /**
     * @param int $companyId
     * @param Request $request
     * @return JsonResponse
     */
    public function getBalances(int $companyId, Request $request): JsonResponse
    {
        $billingProfileId = $request->get('billing_profile_id');

        $credits = $this->service->getCreditBalances(
            companyId       : $companyId,
            billingProfileId: $billingProfileId,
        );

        return $this->formatResponse([
            self::FIELD_STATUS   => true,
            self::FIELD_BALANCES => CompanyCreditBalancesResource::collection($credits),
        ]);
    }

    /**
     * @param int $companyId
     * @return JsonResponse
     */
    public function getCredits(int $companyId): JsonResponse
    {
        $credits = $this->service->getCompanyCredits($companyId);

        return $this->formatResponse([
            self::FIELD_STATUS  => true,
            self::FIELD_CREDITS => new CreditTypeListResource($credits)
        ]);
    }

    /**
     * @param ExpireCreditRequest $request
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function expireCredit(ExpireCreditRequest $request): JsonResponse
    {
        $validated = $request->validated();

        /** @var User $user */
        $user = auth()->user();

        $status = $this->service->expireCredit(
            companyId : Arr::get($validated, ExpireCreditRequest::COMPANY_ID),
            creditId  : Arr::get($validated, ExpireCreditRequest::CREDIT_ID),
            authorType: InvoiceEventAuthorTypes::USER,
            authorId  : $user->id
        );

        return $this->formatResponse([
            self::FIELD_STATUS => $status,
            self::FIELD_DATA   => $status ? new CreditTypeListResource(
                $this->service->getCompanyCredits($validated[ExpireCreditRequest::COMPANY_ID])) : []
        ]);
    }

    /**
     * @param ExtendCreditRequest $request
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function extendCredit(ExtendCreditRequest $request): JsonResponse
    {
        $validated = $request->validated();

        /** @var User $user */
        $user = auth()->user();

        $status = $this->service->extendCredit(
            companyId : Arr::get($validated, ExpireCreditRequest::COMPANY_ID),
            creditId  : Arr::get($validated, ExpireCreditRequest::CREDIT_ID),
            authorType: InvoiceEventAuthorTypes::USER,
            authorId  : $user->id,
            newDate: Arr::get($validated, ExpireCreditRequest::NEW_DATE),
        );

        return $this->formatResponse([
            self::FIELD_STATUS => $status,
            self::FIELD_DATA   => $status ? new CreditTypeListResource($this->service->getCompanyCredits($validated[ExtendCreditRequest::COMPANY_ID])) : []
        ]);

    }

    /**
     * @param GetCreditTypesRequest $request
     * @return AnonymousResourceCollection
     */
    public function getCreditTypes(GetCreditTypesRequest $request): AnonymousResourceCollection
    {
        $validated = $request->validated();

        $creditTypes = $this->service->getCreditTypes(
            isCash: Arr::get($validated, GetCreditTypesRequest::FIELD_IS_CASH)
        );

        return CreditTypesResource::collection($creditTypes);
    }

    /**
     * @param CreateCreditTypeRequest $request
     * @return JsonResponse
     */
    public function createNewCreditType(CreateCreditTypeRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $creditPayload = CreditTypePayload::fromArray($validated);

        return $this->formatResponse([
            self::FIELD_STATUS => !!$this->service->createCreditType($creditPayload),
        ]);
    }

    /**
     * @param UpdateCreditTypesRequest $request
     * @return AnonymousResourceCollection
     */
    public function updateOrCreateCreditTypes(UpdateCreditTypesRequest $request): AnonymousResourceCollection
    {
        $validated = $request->validated();

        $creditTypePayloads = collect();

        foreach ($validated[self::FIELD_CREDIT_TYPES] as $data) {
            $creditTypePayloads->push(CreditTypePayload::fromArray($data));
        }

        $creditTypes = $this->service->updateOrCreateCreditTypes($creditTypePayloads);

        return CreditTypesResource::collection($creditTypes);
    }

    /**
     * @param int $creditId
     * @return AnonymousResourceCollection
     */
    public function getCreditLifeCycleEvents(int $creditId): AnonymousResourceCollection
    {
        $events = $this->service->getCreditLifeCycleEvents($creditId);

        return CreditEventResource::collection($events);
    }
}
