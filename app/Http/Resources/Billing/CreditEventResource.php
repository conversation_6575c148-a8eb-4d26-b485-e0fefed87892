<?php

namespace App\Http\Resources\Billing;

use App\Http\Resources\Odin\BaseJsonResource;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Arr;

class CreditEventResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'event'                 => Arr::get($this->resource, 'event'),
            'date'                  => Arr::get($this->resource, 'date'),
            'credit_type_name'      => Arr::get($this->resource, 'credit_type_name'),
            'credit_type_slug'      => Arr::get($this->resource, 'credit_type_slug'),
            'credit_notes'          => Arr::get($this->resource, 'credit_notes'),
            'company_id'            => Arr::get($this->resource, 'company_id'),
            'company_name'          => Arr::get($this->resource, 'company_name'),
            'amount'                => Arr::get($this->resource, 'amount'),
            'formatted_expiry_date' => Arr::get($this->resource, 'formatted_expiry_date'),
            'is_expired'            => Arr::get($this->resource, 'is_expired'),
            'invoice_id'            => Arr::get($this->resource, 'invoice_id'),
        ];
    }
}
