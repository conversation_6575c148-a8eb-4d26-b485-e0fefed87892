<?php

namespace App\Http\Resources\CompanyCompanyRelationship;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\CompanyCompanyRelationship;
use Illuminate\Http\Request;

/**
 * @mixin CompanyCompanyRelationship
 */
class CompanyCompanyRelationshipResource extends BaseJsonResource
{
    /**
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            'id'                => $this->id,
            'company_name'      => $this->company->name,
            'target_company_name' => $this->targetCompany->name,
            'relationship'      => $this->relationship,
            'created_by_name'   => $this->createdBy->name,
            'deleted_at'        => $this->deleted_at,
            'created_at'        => $this->created_at,
            'updated_at'        => $this->updated_at,
            'active'            => !$this->deleted_at,
        ];
    }
}
