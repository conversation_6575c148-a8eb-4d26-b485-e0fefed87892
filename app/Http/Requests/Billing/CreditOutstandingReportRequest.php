<?php

namespace App\Http\Requests\Billing;

use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class CreditOutstandingReportRequest extends FormRequest
{
    const string FIELD_DATE        = 'date';
    const string FIELD_COMPANY_ID  = 'company_id';
    const string FIELD_INVOICE_ID  = 'invoice_id';
    const string FIELD_CREDIT_TYPE = 'credit_type';
    const string FIELD_ACTION_TYPE = 'action_type';
    const string FIELD_SORT_BY     = 'sort_by';
    const string FIELD_PAGE        = 'page';
    const string FIELD_PER_PAGE    = 'per_page';
    const string FIELD_ALL         = 'all';
    const string FIELD_STATUS      = 'status';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::BILLING_REPORTS_CREDIT_MOVEMENTS_VIEW->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::FIELD_DATE        => 'nullable|string',
            self::FIELD_COMPANY_ID  => 'nullable|numeric',
            self::FIELD_INVOICE_ID  => 'nullable|numeric',
            self::FIELD_CREDIT_TYPE => 'nullable|string',
            self::FIELD_ACTION_TYPE => 'nullable|string',
            self::FIELD_SORT_BY     => 'nullable|array',
            self::FIELD_PAGE        => 'nullable|numeric',
            self::FIELD_PER_PAGE    => 'nullable|numeric',
            self::FIELD_ALL         => 'nullable',
            self::FIELD_STATUS      => 'nullable|string',
        ];
    }
}
