<?php

namespace App\Http\Requests\CompanyCompanyRelationship;

use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdateCompanyCompanyRelationshipRequest extends FormRequest
{
    const string RELATIONSHIP = 'relationship';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            self::RELATIONSHIP => ['required', 'string', 'max:255'],
        ];
    }
}
