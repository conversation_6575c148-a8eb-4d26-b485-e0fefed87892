<?php

namespace App\Http\Requests\CompanyCompanyRelationship;

use App\Enums\PermissionType;
use App\Models\CompanyCompanyRelationship;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class ListCompanyCompanyRelationshipRequest extends FormRequest
{
    const string COMPANY_ID        = 'company_id';
    const string TARGET_COMPANY_ID = 'target_company_id';
    const string RELATIONSHIP      = 'relationship';
    const string PAGE              = 'page';
    const string PER_PAGE          = 'perPage';
    const string ACTIVE            = 'active';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    public function prepareForValidation(): void
    {
        if (!is_null($this->input('active'))) {
            $this->merge([
                'active' => filter_var($this->input('active'), FILTER_VALIDATE_BOOLEAN),
            ]);
        }
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            self::PAGE              => ['integer'],
            self::PER_PAGE          => ['integer', 'min:1', 'max:100'],
            self::COMPANY_ID        => [Rule::exists(CompanyCompanyRelationship::class)],
            self::TARGET_COMPANY_ID => [Rule::exists(CompanyCompanyRelationship::class)],
            self::RELATIONSHIP      => ['string'],
            self::ACTIVE            => ['boolean'],
        ];
    }
}
