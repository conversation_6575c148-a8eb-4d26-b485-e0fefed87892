<?php

namespace App\Services\Odin;

use App\Enums\GlobalConfigurationKey;
use App\Enums\PhoneType;
use App\Models\Odin\Company;
use App\Models\Odin\Consumer;
use App\Models\Odin\ProductAssignment;
use App\Services\GlobalConfigurationService;
use App\Services\Twilio\TwilioPhoneNumberService;
use Exception;
use Illuminate\Support\Arr;

class ConsumerProxyPhoneService
{

    const string PROXY_PHONE_PERCENT = 'proxy_phone_percent';
    const string STATES_EXCLUDED = 'states_excluded';

    /**
     * @param int $companyId
     * @param string $state
     *
     * @return string|null
     */
    public function getProxyPhone(int $companyId, string $state): ?string
    {
        $company = Company::query()->findOrFail($companyId);

        if (!$company->configuration?->consumer_proxy_phone_enabled) {
            return null;
        }

        [$percent, $excludedStates] = $this->getProxyPhoneGlobalConfig();

        if (!$state || array_key_exists(strtolower($state), $excludedStates)) {
            return null;
        }

        $everyNthLead = (int) (100 / $percent);
        $leads = $company->productAssignments()
            ->select([ProductAssignment::FIELD_ID, ProductAssignment::FIELD_PROXY_PHONE, ProductAssignment::FIELD_PROXY_PHONE_ACTIVE])
            ->where(ProductAssignment::FIELD_CREATED_AT, '>=', now()->startOfMonth())
            ->get();
        $proxyPhoneCount = $leads->filter(
            fn(ProductAssignment $pa) => $pa->proxy_phone && $pa->proxy_phone_active
        )->count();

        if ($leads->count() < $everyNthLead) {
            return null;
        }

        if ((int) ($leads->count() / $everyNthLead) <= $proxyPhoneCount) {
            return null;
        }

        try {
            return app(TwilioPhoneNumberService::class)->acquireNumber(
                region: $state,
                type: PhoneType::CONSUMER_PROXY_PHONE,
                name: 'consumer_proxy_phone',
                configuration: [
                    'voiceUrl' => route('consumer-proxy-phone-voice'),
                    'smsUrl'   => route('consumer-proxy-phone-sms'),
                ]
            )->phone;
        } catch (Exception $exception) {
            logger()->error("Failed to get proxy phone for allocation to company: $companyId, error: {$exception->getMessage()}");

            return null;
        }
    }

    protected function getProxyPhoneGlobalConfig(): array
    {
        $config  = app(GlobalConfigurationService::class)->getConfigData(GlobalConfigurationKey::CONSUMER_PROXY_PHONE);
        $percent = Arr::get($config, self::PROXY_PHONE_PERCENT, Consumer::PROXY_PHONE_DEFAULT_PERCENT);
        $states  = collect(
            explode(',', Arr::get($config, self::STATES_EXCLUDED, ''))
        )->map(fn($state) => strtolower(trim($state)))->flip()->toArray();

        return [(int)$percent, $states];
    }
}
