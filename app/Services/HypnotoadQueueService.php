<?php

namespace App\Services;

use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductData;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use JetBrains\PhpStorm\NoReturn;

class HypnotoadQueueService
{
    /**
     * @param int $count
     * @param string|null $stateAbbr
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @param int $maxExistingAssignments
     * @return mixed
     */
    public static function getNextConsumerProducts(
        int $count = 500,
        ?string $stateAbbr = null,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null,
        int $maxExistingAssignments = 2
    ): array
    {
        $defaultEndDate = Carbon::now()->subMonths(27);
        if (is_null($endDate) || $endDate->gt($defaultEndDate)) {
            $endDate = $defaultEndDate;
        }

        // Note: query slows down significantly with more than a one-year date range - once this range is exhausted, we can shift the range back
        if (is_null($startDate)) {
            $startDate = $endDate->clone()->subYear();
        }

        $consumerProducts = self::buildQuery($stateAbbr, $startDate, $endDate, $maxExistingAssignments)
            ->limit($count)
            ->get();

        self::flagAsExported($consumerProducts);

        return $consumerProducts->map(function ($item) {
            $array = is_object($item) ? $item->toArray() : $item;
            unset($array['consumer_product_data_id']);
            return $array;
        })->toArray();
    }

    /**
     * @param string|null $stateAbbr
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @param int $maxExistingAssignments
     * @return Builder
     */
    private static function buildQuery(?string $stateAbbr, ?Carbon $startDate, ?Carbon $endDate, int $maxExistingAssignments = 2): Builder
    {
        $query = ConsumerProduct::query()
            ->select([
                'addresses.state',
                'consumers.created_at',
                'consumers.first_name as name',
                'consumers.formatted_phone as phone',
                DB::raw("CONCAT(addresses.address_1, ' ', addresses.city) as address"),
                DB::raw("'consumer_product' as reference_type"),
                'consumer_products.id as reference_id',
                DB::raw("JSON_EXTRACT(consumer_product_data.payload, '$.electric_cost') as electric_bill"),
                'consumer_products.consumer_product_data_id',
            ])
            ->join('consumer_product_data', 'consumer_products.consumer_product_data_id', '=', 'consumer_product_data.id')
            ->join('consumers', 'consumer_products.consumer_id', '=', 'consumers.id')
            ->join('addresses', 'consumer_products.address_id', '=', 'addresses.id')
            // leaving this commented out, as we will likely re-use this logic once the unlimited budget pool dries up
//            ->join('estimated_revenue_per_lead_by_locations as er', function ($join) {
//                $join->on('addresses.zip_code_location_id', '=', 'er.location_id')
//                    ->where('er.industry_id', 1);
//            })
            ->where('consumer_products.status', '<>', ConsumerProduct::STATUS_CANCELLED)
            ->where('consumers.created_at', '>', $startDate)
            ->where('consumers.created_at', '<', $endDate)
            ->where('consumer_products.service_product_id', 1)
            ->whereNotNull('consumers.formatted_phone')
            ->whereRaw('LENGTH(addresses.state) = 2')
            ->where('consumers.first_name', '!=', 'PRIVACY REQUEST')
            ->whereRaw('LENGTH(consumers.first_name) > 2')
            ->whereRaw("LOWER(consumers.first_name) != 'test'")
            ->whereRaw("(consumer_product_data.payload->>'$.solar_detected' IS NULL OR consumer_product_data.payload->>'$.solar_detected' != 'true')")
            ->whereRaw("consumer_product_data.payload->>'$.exported_to_hypnotoad' IS NULL")
            ->whereIn('addresses.zip_code_location_id', function ($query) {
                $query->select('acl.location_id')
                    ->from('available_company_by_locations as acl')
                    ->join('solarreviews.locations as l', 'acl.location_id', '=', 'l.id')
                    ->where('unlimited_budgets', '>', 0)
                    ->where('industry_slug', '=', 'solar')
                    ->groupBy('location_id')
                    ->havingRaw('sum(unlimited_budgets) > 0');
            })
            ->orderBy(DB::raw('DATE(consumers.created_at)'), 'desc');
//            ->orderBy('er.estimated_revenue', 'desc');

        // Apply state filter if provided
        if ($stateAbbr) {
            $query->where('addresses.state', $stateAbbr);
        }

        // Apply existing assignments filter if enabled (subquery positioned optimally)
        if ($maxExistingAssignments >= 0) {
            $query->whereRaw("
                (SELECT COUNT(*) FROM product_assignments
                 WHERE product_assignments.consumer_product_id = consumer_products.id) <= ?
            ", [$maxExistingAssignments]);
        }

        return $query;
    }

    /**
     * @param Collection $consumerProducts
     * @return void
     */
    #[NoReturn] private static function flagAsExported(Collection $consumerProducts): void
    {
        ConsumerProductData::whereIn(ConsumerProductData::FIELD_ID, $consumerProducts->pluck('consumer_product_data_id'))
            ->update(['payload->exported_to_hypnotoad' => true]);
    }

}
