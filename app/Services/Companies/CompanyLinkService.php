<?php

namespace App\Services\Companies;

use App\Models\Odin\Company;
use App\Services\Companies\Delete\CompanyDeleteService;

class CompanyLinkService
{
    public function getDashboardUrl(Company $company): string
    {
        return $company->getAdminProfileUrl();
    }

    public function getCancelDeletionUrl(Company $company): string
    {
        $deleteService = new CompanyDeleteService($company);
        return $deleteService->cancelUrl();
    }


}
