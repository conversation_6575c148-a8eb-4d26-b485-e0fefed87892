<?php

namespace App\Services\QAAutomation\AutomationServices;

use App\Enums\GlobalConfigurationKey;
use App\Enums\Odin\SolarConfigurableFields;
use App\Models\Odin\ConsumerProduct;
use App\Models\QAAutomation\QAAutomationLog;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Services\GlobalConfigurationService;
use Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Collection;
use Exception;

class SolarQAAutomationClassificationService
{
    const string CLASSIFIER_MODEL = 'classifier_model';
    const string CLASSIFIER_CONFIDENCE_PERCENTAGE = 'classifier_confidence_percentage';
    private string $apiUrl;
    private string $apiKey;
    private int $timeout;
    private ?string $classifierModel;
    private ?string $classifierConfidence;
    private ConsumerProductRepository $consumerProductRepository;

    public function __construct(ConsumerProductRepository $consumerProductRepository, GlobalConfigurationService $globalConfigurationService)
    {
        // Configure these in your .env file
        $this->apiUrl = config('services.solar_classifier.url');
        $this->apiKey = config('services.solar_classifier.api_key');
        $this->timeout = 30;
        $this->consumerProductRepository = $consumerProductRepository;
        $this->classifierModel = Arr::get($globalConfigurationService->getConfigData(GlobalConfigurationKey::SOLAR_CLASSIFIER), self::CLASSIFIER_MODEL);
        $this->classifierConfidence = Arr::get($globalConfigurationService->getConfigData(GlobalConfigurationKey::SOLAR_CLASSIFIER), self::CLASSIFIER_CONFIDENCE_PERCENTAGE);
    }
    public function qualifySolarLead(ConsumerProduct $consumerProduct): bool
    {
        /**
         * Criteria to qualify a solar lead from calculator inputs
         *
         *
         * Power bill above 80
         * How much shade is on your roof during the hours of 10 am to 3 pm - 'none'
         * Do you currently own or have authority with respect to this house? - 'yes'
         * Please describe this home - Freestanding
         *
         * Do you currently have solar panels on your home? - 'NO'
         * OR
         * Do you currently have solar panels on your home? - 'Yes'
         * +
         *  a. Currently only for hot water, looking for PV ||
         *  b. I am looking for battery storage ||
         *  c. I want to expand my solar system size ||
         */

        $consumerProductDataPayload = $consumerProduct->consumerProductData->payload;

        if (
            (int)Arr::get($consumerProductDataPayload, SolarConfigurableFields::ELECTRIC_COST->value) > 80 &&
            Arr::get($consumerProductDataPayload, SolarConfigurableFields::HOME_TYPE->value) === 'freestanding' &&
            (
                Arr::get($consumerProductDataPayload, SolarConfigurableFields::CURRENT_SOLAR->value) === 'no' ||
                (
                    Arr::get($consumerProductDataPayload, SolarConfigurableFields::CURRENT_SOLAR->value) === 'yes' &&
                    Arr::get($consumerProductDataPayload, SolarConfigurableFields::SOLAR_NEEDS->value) !== 'pricing'
                )
            )
        ) {
            //check if they dont have solar
            if($this->hasExistingSolarArray($consumerProduct)) {
                QAAutomationLog::create([
                    QAAutomationLog::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
                    QAAutomationLog::FIELD_ENTRY => 'Solar lead failed QA',
                    QAAutomationLog::FIELD_ERROR_MESSAGE => 'Solar Api return a match for an existing solar system',
                    QAAutomationLog::FIELD_LOG_LEVEL => QAAutomationLog::LEVEL_LOG,
                ]);

                return false;
            }

            if(!$this->qualifyViaImageClassificationForSolar($consumerProduct)) {
                return false;
            }

            return true;

        } else {
            QAAutomationLog::create([
                QAAutomationLog::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
                QAAutomationLog::FIELD_ENTRY => 'Solar lead failed QA',
                QAAutomationLog::FIELD_ERROR_MESSAGE => json_encode($consumerProductDataPayload),
                QAAutomationLog::FIELD_LOG_LEVEL => QAAutomationLog::LEVEL_LOG,
            ]);
        }

        return false;
    }

    protected function hasExistingSolarArray(ConsumerProduct $consumerProduct): bool
    {
        // Check if we already have a Google Solar API response
        $existingResponse = Arr::get($consumerProduct->consumerProductData?->payload, 'google_solar_api_response');

        if ($existingResponse && isset($existingResponse['hasInstallations'])) {
            return $existingResponse['hasInstallations'];
        }

        $requestParams = [
            'location.latitude' => $consumerProduct->address->latitude,
            'location.longitude' => $consumerProduct->address->longitude,
            'key' => config('services.google.solar.api_key')
        ];

        $response = Http::get(config('services.google.solar.base_url'), $requestParams);

        if ($response->successful()) {
            $responseData = $response->json();
            
            // Save the Google Solar API response to consumer product data
            try {
                $apiResponseData = new Collection(['google_solar_api_response' => $responseData]);
                $this->consumerProductRepository->updateConsumerProductModelAndPayloadById(
                    $consumerProduct->id,
                    $apiResponseData,
                    [], // No model keys to update
                    ['google_solar_api_response'] // Save to payload
                );
            } catch (Exception $e) {
                Logger()->error("Failed to save Google Solar API response", [
                    'consumer_product_id' => $consumerProduct->id,
                    'error' => $e->getMessage()
                ]);
            }
            
            return Arr::get($responseData, 'hasInstallations');
        } else {
            logger()->error("Couldn't retrieve phone data from Google Solar Api");
        }

        //return true to be safe because the api call has failed
        return true;
    }

    public function qualifyViaImageClassificationForSolar(ConsumerProduct $consumerProduct): bool
    {
        try {
            // Check if we already have a solar classification API response
            $existingResponse = Arr::get($consumerProduct->consumerProductData?->payload, 'solar_classification_api_response');

            if ($existingResponse && isset($existingResponse['classification'])) {
                return $this->evaluateSolarSuitability($existingResponse['classification']);
            }

            $latitude = $consumerProduct->address->latitude;
            $longitude = $consumerProduct->address->longitude;
            
            if (!$latitude || !$longitude) {
                QAAutomationLog::create([
                    QAAutomationLog::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
                    QAAutomationLog::FIELD_ENTRY => 'Solar image classification failed',
                    QAAutomationLog::FIELD_ERROR_MESSAGE => 'Missing latitude or longitude coordinates',
                    QAAutomationLog::FIELD_LOG_LEVEL => QAAutomationLog::LEVEL_ERROR,
                ]);
                return false;
            }

            $result = $this->callSolarClassificationAPI($latitude, $longitude);
            
            if (!$result['success']) {
                QAAutomationLog::create([
                    QAAutomationLog::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
                    QAAutomationLog::FIELD_ENTRY => 'Solar image classification failed',
                    QAAutomationLog::FIELD_ERROR_MESSAGE => $result['error'] ?? 'Unknown error',
                    QAAutomationLog::FIELD_LOG_LEVEL => QAAutomationLog::LEVEL_ERROR,
                ]);
                return false;
            }

            $classification = $result['classification'];
            
            // Save the API response to consumer product data
            try {
                $apiResponseData = new Collection(['solar_classification_api_response' => $result]);
                $this->consumerProductRepository->updateConsumerProductModelAndPayloadById(
                    $consumerProduct->id,
                    $apiResponseData,
                    [], // No model keys to update
                    ['solar_classification_api_response'] // Save to payload
                );
            } catch (Exception $e) {
                Logger()->error("Failed to save solar classification API response", [
                    'consumer_product_id' => $consumerProduct->id,
                    'error' => $e->getMessage()
                ]);
            }

            QAAutomationLog::create([
                QAAutomationLog::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
                QAAutomationLog::FIELD_ENTRY => 'Solar image classification completed',
                QAAutomationLog::FIELD_ERROR_MESSAGE => json_encode([
                    'detailed_class' => $classification['detailed_class'],
                    'confidence' => $classification['confidence'],
                    'solar_suitable' => $classification['solar_suitable'],
                    'coordinates' => $result['coordinates']
                ]),
                QAAutomationLog::FIELD_LOG_LEVEL => QAAutomationLog::LEVEL_LOG,
            ]);

            return $this->evaluateSolarSuitability($classification);

        } catch (Exception $e) {
            QAAutomationLog::create([
                QAAutomationLog::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
                QAAutomationLog::FIELD_ENTRY => 'Solar image classification error',
                QAAutomationLog::FIELD_ERROR_MESSAGE => $e->getMessage(),
                QAAutomationLog::FIELD_LOG_LEVEL => QAAutomationLog::LEVEL_ERROR,
            ]);

            return false;
        }
    }

    /**
     * Call the solar classification API for coordinates
     *
     * @param float $latitude
     * @param float $longitude
     * @return array
     */
    private function callSolarClassificationAPI(float $latitude, float $longitude): array
    {
        try {
            $this->validateCoordinates($latitude, $longitude);

            $headers = [
                'X-API-Key' => $this->apiKey,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ];

            $requestData = [
                'latitude' => $latitude,
                'longitude' => $longitude
            ];

            if (!empty($this->classifierModel)) {
                $requestData['model'] = $this->classifierModel;
            }

            $response = Http::timeout($this->timeout)
                ->withHeaders($headers)
                ->post("{$this->apiUrl}/classify", $requestData);

            if (!$response->successful()) {
                throw new Exception("API request failed with status: " . $response->status() . " - " . $response->body());
            }

            $data = $response->json();

            if (isset($data['status']) && $data['status'] === 'error') {
                throw new Exception("Classification failed: " . ($data['error'] ?? 'Unknown error'));
            }

            return $this->formatClassificationResult($data, $latitude, $longitude);

        } catch (Exception $e) {
            Logger()->error("Solar classification API error", [
                'latitude' => $latitude,
                'longitude' => $longitude,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'coordinates' => ['latitude' => $latitude, 'longitude' => $longitude]
            ];
        }
    }

    /**
     * Evaluate if the classification result indicates solar suitability
     *
     * @param array $classification
     * @return bool
     */
    private function evaluateSolarSuitability(array $classification): bool
    {
        // Must be marked as solar suitable by the AI
        if (!$classification['solar_suitable']) {
            return false;
        }

        // Must have reasonable confidence
        if ($classification['confidence'] < $this->classifierConfidence ?? 90) {
            return false;
        }

        // 'good' classes are acceptable for solar installation
        // 'shade', 'solar', 'mobile_home', and 'no_house' are not suitable
        $acceptableClasses = ['good'];
        
        return in_array($classification['detailed_class'], $acceptableClasses);
    }

    /**
     * Validate latitude and longitude coordinates
     *
     * @param float $latitude
     * @param float $longitude
     * @throws Exception
     */
    private function validateCoordinates(float $latitude, float $longitude): void
    {
        if ($latitude < -90 || $latitude > 90) {
            throw new Exception("Invalid latitude: {$latitude}. Must be between -90 and 90.");
        }

        if ($longitude < -180 || $longitude > 180) {
            throw new Exception("Invalid longitude: {$longitude}. Must be between -180 and 180.");
        }
    }

    /**
     * Format the API response into a standardized result
     *
     * @param array $apiResponse
     * @param float $latitude
     * @param float $longitude
     * @return array Formatted result
     */
    private function formatClassificationResult(array $apiResponse, float $latitude, float $longitude): array
    {
        return [
            'success' => true,
            'coordinates' => [
                'latitude' => $latitude,
                'longitude' => $longitude
            ],
            'classification' => [
                'detailed_class' => $apiResponse['predictions']['detailed']['class'] ?? null,
                'confidence' => $apiResponse['predictions']['detailed']['confidence'] ?? 0,
                'binary_class' => $apiResponse['predictions']['binary']['suitable'] ?? null,
                'solar_suitable' => $apiResponse['predictions']['binary']['suitable'] ?? false,
                'suitability_probability' => $apiResponse['predictions']['binary']['confidence'] ?? 0
            ],
            'probabilities' => $apiResponse['predictions']['detailed']['probabilities'] ?? [],
            'processing_time_seconds' => $apiResponse['processing_time_seconds'] ?? null,
            'model_info' => $apiResponse['model_info'] ?? null,
            'classified_at' => now()->toISOString()
        ];
    }
}